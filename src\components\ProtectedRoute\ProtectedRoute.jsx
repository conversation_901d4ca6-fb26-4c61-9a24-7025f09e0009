// src/components/ProtectedRoute/ProtectedRoute.jsx
import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useModal } from '@/features/modals/model/context';

/**
 * Protected route component that checks if user is authenticated
 * before allowing access to the route.
 *
 * Uses the useAuthContext hook which directly checks cookies.
 */
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated } = useAuthContext();
  const location = useLocation();
  const { showModal } = useModal();

  // Show auth modal if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      showModal({
        type: 'auth',
        content: 'checkAuth',
        from: location
      });
    }
  }, [isAuthenticated, location, showModal]);

  // Redirect if not authenticated
  if (!isAuthenticated) {
    // Log in development mode
    if (process.env.NODE_ENV === 'development') {
      console.debug('[ProtectedRoute] Not authenticated, redirecting from:', location.pathname);
    }
    return <Navigate to="/" />;
  }

  // Auth check passed, render children
  return children;
};

export default ProtectedRoute;
