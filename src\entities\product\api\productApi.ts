// src/entities/product/api/productApi.ts
import { api } from '@/shared/api/api';
import { GetProductResponse, GetVariantsRequest, GetVariantsResponse } from './types';

export const productApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getProduct: builder.query<GetProductResponse, string>({
      query: (id) => `api/Products/item?id=${id}`,
      providesTags: (result, error, id) => [{ type: 'Product', id }],
      keepUnusedDataFor: 60 // Cache product data for 60 seconds
    }),
    getVariants: builder.mutation<GetVariantsResponse, GetVariantsRequest>({
      query: (params) => ({
        url: '/api/Products/variants',
        method: 'POST',
        body: params
      }),
      // Add query caching for 30 seconds to prevent duplicate requests
      keepUnusedDataFor: 30,
      // Deduplicate identical requests during that window
      extraOptions: {
        maxRetries: 0 // Disable retries for variants
      },
      // Force sequential execution
      async onQueryStarted(args, { dispatch, queryFulfilled, getCacheEntry }) {
        try {
          // Wait for any existing mutations to complete
          const existingEntry = getCacheEntry();
          if (existingEntry?.status === 'pending') {
            await queryFulfilled;
          }
        } catch {}
      },
      // Serialize request for cache key
      serializeQueryArgs: ({ queryArgs }) => {
        return JSON.stringify(queryArgs);
      },
      // Only trigger if args changed
      forceRefetch: ({ currentArg, previousArg }) => {
        return JSON.stringify(currentArg) !== JSON.stringify(previousArg);
      }
    }),
    getVariantsTest: builder.mutation<GetVariantsResponse, GetVariantsRequest>({
      query: (params) => ({
        url: '/api/ProductsTest/variants',
        method: 'POST',
        body: params
      }),
      // Add query caching for 30 seconds to prevent duplicate requests

      // Deduplicate identical requests during that window
      extraOptions: {
        maxRetries: 0 // Disable retries for variants
      },
      // Force sequential execution
      async onQueryStarted(args, { dispatch, queryFulfilled, getCacheEntry }) {
        try {
          // Wait for any existing mutations to complete
          const existingEntry = getCacheEntry();
          if (existingEntry?.status === 'pending') {
            await queryFulfilled;
          }
        } catch {}
      }
    })
  })
});

export const { useGetProductQuery, useGetVariantsMutation, useGetVariantsTestMutation } =
  productApi;
