import React from 'react';

import { useSelector } from 'react-redux';
import { NavLink } from 'react-router-dom';

import {
  RatingStars,
  Review,
  useGetReviewsQuery,
  useHasUserReview,
} from '@/entities/review';
import { useModal } from '@/features/modals/model/context';
import { Button } from '@/shared/ui';

const ReviewsTab = ({ current }) => {
  const { showModal } = useModal();
  const { isAuthenticated } = useSelector((state) => state?.user);
  const { hasReview } = useHasUserReview(current?.slug);

  const { data, refetch } = useGetReviewsQuery(current?.id, {
    skip: !current?.id,
    refetchOnMountOrArgChange: true,
  });

  const reviews = data?.comments || [];
  const totalReviews = data?.total_count || 0;
  const averageRating = Number(data?.avg_rating || 0).toFixed(1);

  return (
    <>
      <h3 className="text-2xl mt-5 font-semibold" id="reviews">
        Отзывы
      </h3>
      <div className="flex justify-between my-5">
        <div className="flex items-center">
          <div className="text-2xl font-semibold mr-2">{averageRating}</div>
          <div className="flex items-center mr-2">
            <RatingStars totalStars={5} initialRating={Number(averageRating)} />
          </div>
          <div className="text-lg text-colDarkGray">
            {totalReviews > 0
              ? `${totalReviews} ${totalReviews === 1 ? 'отзыв' : 'отзывов'}`
              : 'Нет отзывов'}
          </div>
        </div>
        {!hasReview ? (
          <Button
            onClick={() => {
              if (isAuthenticated) {
                showModal({ type: 'review', variantId: current?.id });
              } else {
                showModal({ type: 'auth' });
              }
            }}
            variant="outline"
            size="lg"
          >
            Оставить отзыв
          </Button>
        ) : null}
        {hasReview ? (
          <Button onClick={() => {}} variant="outline" size="lg" disabled>
            Вы уже оставили отзыв об этом товаре
          </Button>
        ) : null}
      </div>
      {reviews?.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          {reviews.map((review) => (
            <Review key={review.created_at} review={review} variant_id={data?.variant_id} />
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-colDarkGray">
          Пока нет отзывов. Будьте первым, кто оставит отзыв!
        </div>
      )}
      {reviews?.length > 4 ? (
        <div className="mt-4 text-center">
          <NavLink
            to={`/catalog/${current?.category?.slug}/${current?.slug}/reviews`}
            className="text-colGreen font-semibold underline underline-offset-8 cursor-pointer mt-5"
          >
            Смотреть все отзывы
          </NavLink>
        </div>
      ) : null}
    </>
  );
};

export default ReviewsTab;
