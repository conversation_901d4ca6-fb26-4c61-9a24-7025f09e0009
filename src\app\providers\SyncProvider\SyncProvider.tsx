// src/app/providers/SyncProvider/SyncProvider.tsx
import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useCart } from '@/features/cart/model/hooks/useCart';
import { useFavorites } from '@/features/favorite/model/hooks/useFavorites';
import { useComparison } from '@/features/comparison/model/hooks/useComparison';
import { useGetUserDataQuery } from '@/entities/user/api/userApi';
import { setUserData } from '@/entities/user';
export interface SyncContextType {
  isInitialized: boolean;
  syncOnLogin: () => Promise<void>;
  syncOnLogout: () => void;
}

const SyncContext = createContext<SyncContextType | null>(null);

export const useSyncContext = (): SyncContextType => {
  const context = useContext(SyncContext);
  if (!context) {
    throw new Error('useSyncContext must be used within a SyncProvider');
  }
  return context;
};

export const SyncProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  console.log('🔄 SyncProvider render');

  const dispatch = useDispatch();
  const { isAuthenticated, isInitialized: authInitialized } = useAuthContext();
  const [isInitialized, setIsInitialized] = useState(false);
  const initOnceRef = useRef(false);

  console.log('📊 SyncProvider state:', {
    authInitialized,
    isAuthenticated,
    isInitialized,
    initOnceRef: initOnceRef.current
  });

  // Entity hooks
  const {
    loadFromStorage: loadCartFromStorage,
    refreshFromServer: refreshCartFromServer,
    clearData: clearCartData,
    syncToServer: syncCartToServer,
    cart: cartData
  } = useCart();

  const {
    loadFromStorage: loadFavoritesFromStorage,
    refreshFromServer: refreshFavoritesFromServer,
    clearData: clearFavoritesData,
    syncToServer: syncFavoritesToServer,
    favorites: favoritesData
  } = useFavorites();

  const {
    loadFromStorage: loadComparisonFromStorage,
    refreshFromServer: refreshComparisonFromServer,
    clearData: clearComparisonData,
    syncToServer: syncComparisonToServer,
    comparison: comparisonData
  } = useComparison();

  // Track previous auth state for login/logout detection
  const prevAuthRef = useRef<boolean | null>(null);

  // User data fetching
  const { data: userData } = useGetUserDataQuery(undefined, {
    skip: !authInitialized || !isAuthenticated,
  });

  // Update user data in Redux when available
  useEffect(() => {
    if (userData?.user && isAuthenticated) {
      console.log('👤 User data received, updating Redux');
      dispatch(setUserData(userData.user));
    }
  }, [userData, dispatch, isAuthenticated]);

  // Login sync function
  const syncOnLogin = useCallback(async (): Promise<void> => {
    try {
      console.log('🔄 Starting login sync...');

      // Step 1: Upload local data to server
      const uploadPromises: Promise<any>[] = [];

      // Upload cart data
      if (cartData?.cart && cartData.cart.length > 0) {
        uploadPromises.push(
          syncCartToServer(cartData.cart).catch((err) => {
            console.warn('[SyncProvider] Error uploading cart:', err);
          })
        );
      }

      // Upload favorites data
      if (favoritesData && favoritesData.length > 0) {
        uploadPromises.push(
          syncFavoritesToServer(favoritesData).catch((err) => {
            console.warn('[SyncProvider] Error uploading favorites:', err);
          })
        );
      }

      // Upload comparison data
      if (comparisonData && comparisonData.length > 0) {
        uploadPromises.push(
          syncComparisonToServer(comparisonData).catch((err) => {
            console.warn('[SyncProvider] Error uploading comparison:', err);
          })
        );
      }

      // Wait for all uploads to complete
      if (uploadPromises.length > 0) {
        await Promise.allSettled(uploadPromises);
        console.log('✅ Local data uploaded to server');
      }

      // Step 2: Fetch fresh data from server
      console.log('📥 Fetching fresh data from server...');
      await Promise.allSettled([
        refreshCartFromServer(),
        refreshFavoritesFromServer(),
        refreshComparisonFromServer(),
      ]);

      console.log('✅ Login sync completed');
    } catch (error) {
      console.error('❌ Error during login sync:', error);
    }
  }, [
    cartData,
    favoritesData,
    comparisonData,
    syncCartToServer,
    syncFavoritesToServer,
    syncComparisonToServer,
    refreshCartFromServer,
    refreshFavoritesFromServer,
    refreshComparisonFromServer,
  ]);

  // Logout sync function
  const syncOnLogout = useCallback((): void => {
    console.log('🔄 Starting logout sync...');
    clearCartData();
    clearFavoritesData();
    clearComparisonData();
    setIsInitialized(false);
    initOnceRef.current = false;
    console.log('✅ Logout sync completed');
  }, [clearCartData, clearFavoritesData, clearComparisonData]);

  // Simplified initialization effect
  useEffect(() => {
    console.log('🎯 SyncProvider useEffect:', {
      authInitialized,
      isAuthenticated,
      isInitialized,
      initOnceRef: initOnceRef.current,
    });

    // Step 1: Initialize once when auth is ready
    if (authInitialized && !initOnceRef.current) {
      console.log('✅ Starting initialization...');
      initOnceRef.current = true;

      if (isAuthenticated) {
        console.log('📥 Authenticated - fetching from server...');
        // Fetch from server for authenticated users
        void Promise.allSettled([
          refreshCartFromServer(),
          refreshFavoritesFromServer(),
          refreshComparisonFromServer(),
        ]).finally(() => {
          setIsInitialized(true);
        });
      } else {
        console.log('💾 Not authenticated - loading from storage...');

        // Debug: Check what's in sessionStorage
        console.log('🔍 SessionStorage contents:', {
          cart: sessionStorage.getItem('cart'),
          favorite: sessionStorage.getItem('favorite'),
          comparison: sessionStorage.getItem('comparison'),
        });

        // Load from storage for non-authenticated users
        loadCartFromStorage();
        loadFavoritesFromStorage();
        loadComparisonFromStorage();
        setIsInitialized(true);
      }
    }
  }, [authInitialized, isAuthenticated]);

  // Separate effect for handling auth state changes (login/logout)
  useEffect(() => {
    // Only handle changes after initial setup
    if (!initOnceRef.current || prevAuthRef.current === null) {
      prevAuthRef.current = isAuthenticated;
      return;
    }

    // User just logged in
    if (!prevAuthRef.current && isAuthenticated) {
      console.log('🔄 User logged in - triggering login sync');
      void syncOnLogin();
    }

    // User just logged out
    if (prevAuthRef.current && !isAuthenticated) {
      console.log('🔄 User logged out - triggering logout sync');
      syncOnLogout();
    }

    // Update previous auth state
    prevAuthRef.current = isAuthenticated;
  }, [isAuthenticated, syncOnLogin, syncOnLogout]);

  const contextValue: SyncContextType = {
    isInitialized,
    syncOnLogin,
    syncOnLogout,
  };

  return (
    <SyncContext.Provider value={contextValue}>
      {children}
    </SyncContext.Provider>
  );
};
