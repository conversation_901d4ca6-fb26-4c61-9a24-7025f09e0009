// src/features/cart/ui/controls/QuantityControl.tsx
import { AddOutlined, RemoveOutlined } from '@mui/icons-material';
import { useQuantityControl } from '@/features/cart';
import type { CartProduct } from '@/features/cart';

interface QuantityControlProps {
  product: CartProduct;
  enableRemove?: boolean;
  className?: string;
  showMessage?: boolean;
}

export const QuantityControl = ({
  product,
  enableRemove = false,
  className = '',
  showMessage = true,
}: QuantityControlProps) => {
  const { 
    quantity, 
    isLoading, 
    handleIncrease, 
    handleDecrease, 
    isMinQuantity,
    isMaxQuantity,
    stockLimitMessage
  } = useQuantityControl({ 
    product, 
    enableRemove 
  });

  return (
    <div className={`flex flex-col  w-full ${className}`}>
      
      <div className="flex justify-between w-full items-center grow bg-colLightGray h-10 px-2 py-1 rounded-md">
        <button
          className={`${isLoading ? 'cursor-wait' : 'cursor-pointer'} w-8 h-8 hover:bg-white rounded-md flex items-center justify-center`}
          onClick={handleDecrease}
          disabled={isLoading || (isMinQuantity && !enableRemove)}
        >
          <RemoveOutlined
            className={`${isMinQuantity && !enableRemove ? 'text-colGray' : 'text-colGreen'}`}
          />
        </button>

        <span className="text-colGreen font-semibold px-5">
          {quantity}
        </span>

        <button
          className={`${isLoading ? 'cursor-wait' : 'cursor-pointer'} w-8 h-8 hover:bg-white rounded-md flex items-center justify-center`}
          onClick={handleIncrease}
          disabled={isLoading || isMaxQuantity}
        >
          <AddOutlined className={`${isMaxQuantity ? 'text-colGray' : 'text-colGreen'}`} />
        </button>
      </div>
       {/* Always render the message container with fixed height to prevent layout shifts */}
       {/* <div className="h-5 text-xs text-red-500 mt-1 text-center">
        {showMessage && stockLimitMessage}
      </div> */}
     
    </div>
  );
};