import { ComparisonButton } from '@/features/comparison/';
import { FavoriteButton } from '@/features/favorite/';
import { useModal } from '@/features/modals/model/context';
import share from '@/shared/assets/icons/share-gray.svg';

const MobileTopBar = ({ product }) => {
  const { showModal } = useModal();

  return (
    <div className="flex justify-end gap-3 my-2">
      <button
        className="text-center flex flex-row justify-between items-center"
        onClick={() => showModal({ type: 'share' })}
      >
        <img className="" src={share} alt="*" />
      </button>

      <ComparisonButton product={product} className="text-center flex flex-row justify-between items-center" />
      <FavoriteButton product={product} className="text-center flex flex-row justify-between items-center" />
    </div>
  );
};

export default MobileTopBar;
