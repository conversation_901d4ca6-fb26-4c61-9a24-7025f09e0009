import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { useCountries } from '../../hooks/useCountries';
import { usePhoneVerification } from '../../hooks/usePhoneVerification';
import { usePhoneMask } from '../../hooks/usePhoneMask';
import CountrySelector from '../CountrySelector';
import VerificationInput from '../VerificationInput';
import { cleanPhoneNumber, stripCountryCode } from '../../utils/formatters';
import '../../gx-phone-react.css';

/**
 * PhoneInput - A modular phone input component with optional verification
 *
 * @param {Object} props - Component props
 * @param {Function} props.onVerified - Callback when verification is completed
 * @param {Function} props.onVerificationSuccess - Callback with session ID after successful verification
 * @param {boolean} props.requiredVerification - Whether verification is required
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.placeholder - Input placeholder text
 * @param {boolean} props.disabled - Disable the input
 * @param {string} props.value - Controlled input value
 * @param {Function} props.onChange - Called when input value changes
 * @param {Function} props.onValid - Called when validation state changes
 * @param {string} props.apiUrl - API URL for verification endpoints
 * @returns {JSX.Element}
 */
export const GxPhoneInput = forwardRef(({
  // Callbacks
  onVerified,
  onVerificationSuccess,
  onChange,
  onValid,

  // Configuration
  requiredVerification = false,
  className = '',
  placeholder = 'Phone number',
  disabled = false,
  value: externalValue,

  // API options
  apiUrl = 'https://phone.gexarus.com/api',
}, ref) => {
  // Component state
  const [verificationStage, setVerificationStage] = useState('idle'); // idle, ready, sending, sent, verified
  const [verificationCode, setVerificationCode] = useState('');
  const [message, setMessage] = useState({ text: '', type: '', action: null });
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [isNumberValid, setIsNumberValid] = useState(false);
  const [processingAPIResponse, setProcessingAPIResponse] = useState(false);
  const [userTyping, setUserTyping] = useState(false);

  // Refs
  const containerRef = useRef(null);
  const inputRef = useRef(null);
  const verificationInputRef = useRef(null);
  const validationTimeoutRef = useRef(null);
  const countryDetectionTimeoutRef = useRef(null);
  const userInputTimeoutRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const lastAPIRequestRef = useRef({ timestamp: 0, country: null, value: '' });
  const restoringFocusRef = useRef(false);
  const isActivelyTypingRef = useRef(false);

  // Custom hooks
  const {
    countries,
    defaultCountry,
    isLoading: isCountriesLoading
  } = useCountries(apiUrl);

  const {
    validateNumber,
    getNumberInfo,
    sendVerificationCode,
    verifyCode,
    isVerifying,
    sessionId
  } = usePhoneVerification(apiUrl);

  // Use the phoneMask hook for input formatting
  const {
    value: inputValue,
    rawValue: phoneDigits,
    setInputValue,
    updateMask,
    handleChange: handlePhoneInputChange,
    reset: resetPhoneInput,
    maskApplied
  } = usePhoneMask(externalValue || '', '', (e) => {
    if (onChange) {
      onChange(e.target.value);
    }
  });

  // Set default country when loaded and apply initial mask
  useEffect(() => {
    if (defaultCountry && !selectedCountry) {
      setSelectedCountry(defaultCountry);

      // Apply mask when default country is set
      if (defaultCountry.mask && defaultCountry.mask.local) {
        updateMask(defaultCountry.mask.local);
      }
    }
  }, [defaultCountry, selectedCountry, updateMask]);

  // Maintain focus - more robust focus handling
  const restoreFocus = () => {
    // NEVER try to restore focus when the user is actively typing
    if (restoringFocusRef.current || isActivelyTypingRef.current) return;

    // Don't steal focus from country search input
    if (document.activeElement &&
        document.activeElement.classList.contains('gx-country-search-input')) {
      return;
    }

    if (inputRef.current && document.activeElement !== inputRef.current) {
      restoringFocusRef.current = true;

      try {
        const cursorPosition = inputRef.current.value.length;
        inputRef.current.focus();

        // Also try to set the cursor position at the end
        if (inputRef.current.setSelectionRange) {
          setTimeout(() => {
            try {
              inputRef.current.setSelectionRange(cursorPosition, cursorPosition);
            } catch (e) {
              console.error("Couldn't set selection range:", e);
            }
            restoringFocusRef.current = false;
          }, 0);
        } else {
          restoringFocusRef.current = false;
        }
      } catch (e) {
        console.error("Couldn't restore focus:", e);
        restoringFocusRef.current = false;
      }
    }
  };

  // Restore focus when messages change or API processing completes
  // ONLY if user is not actively typing
  useEffect(() => {
    if (!isActivelyTypingRef.current &&
        (message.text || processingAPIResponse === false) &&
        !restoringFocusRef.current) {
      restoreFocus();
    }
  }, [message, processingAPIResponse]);

  // Sync value prop with internal state
  useEffect(() => {
    if (externalValue !== undefined && externalValue !== inputValue) {
      setInputValue(externalValue);
    }
  }, [externalValue, inputValue, setInputValue]);

  // Indicate user is typing - and create a "cooldown" after typing stops
  const handleUserTyping = () => {
    setUserTyping(true);
    isActivelyTypingRef.current = true;

    // Clear any existing typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to clear the typing state
    typingTimeoutRef.current = setTimeout(() => {
      setUserTyping(false);
      isActivelyTypingRef.current = false;

      // Schedule country detection after typing stops - but don't interrupt typing
      if (inputRef.current && inputRef.current.value) {
        checkCountryFromInput(inputRef.current.value);
      }
    }, 300); // Reduced from 1000ms to 300ms for better responsiveness
  };

  // Rate limiting for API requests - prevent too many calls in succession
  const canMakeAPIRequest = (phoneNumber, countryIso) => {
    const now = Date.now();
    const lastRequest = lastAPIRequestRef.current;

    // Don't check numbers that are too short
    if (!phoneNumber || cleanPhoneNumber(phoneNumber).length < 3) {
      return false;
    }

    // While user is actively typing, don't make API requests that could potentially block
    if (isActivelyTypingRef.current) {
      return false;
    }

    // Apply rate limiting
    if (now - lastRequest.timestamp < 200) { // Reduced to 200ms for more responsiveness
      return false;
    }

    // If we just made a request for this country & exact same value, don't repeat it
    if (lastRequest.country === countryIso &&
        lastRequest.value === phoneNumber &&
        now - lastRequest.timestamp < 2000) { // Reduced to 2 seconds
      return false;
    }

    return true;
  };

  // Record an API request was made
  const recordAPIRequest = (phoneNumber, countryIso) => {
    lastAPIRequestRef.current = {
      timestamp: Date.now(),
      country: countryIso,
      value: phoneNumber
    };
  };

  // Check for country on each keystroke - but ONLY after typing has stopped
  const checkCountryFromInput = async (phoneNumber) => {
    if (!phoneNumber || !selectedCountry) return;

    if (!canMakeAPIRequest(phoneNumber, selectedCountry.iso)) {
      return;
    }

    // Record that we're making this request
    recordAPIRequest(phoneNumber, selectedCountry.iso);

    try {
      // Indicate we're processing an API response - but don't block input
      setProcessingAPIResponse(true);

      const result = await getNumberInfo(
        phoneNumber,
        selectedCountry.iso,
        selectedCountry.code
      );

      if (result.success) {
        // Check if the country has changed
        if (result.country && result.country.iso !== selectedCountry.iso) {
          // Find the matching country in our countries list
          const detectedCountry = countries.find(
            c => c.iso === result.country.iso
          );

          // Instead of auto-switching, show a suggestion
          if (detectedCountry) {
            console.log('Detected different country:', detectedCountry.en);

            // Only show message if user isn't currently typing
            if (!isActivelyTypingRef.current) {
              setMessage({
                text: `Похоже, это номер из ${detectedCountry.ru || detectedCountry.en}. Переключить?`,
                type: 'info',
                action: {
                  label: 'Да, переключить',
                  callback: () => {
                    setSelectedCountry(detectedCountry);

                    // Update the mask based on the detected country
                    if (result.mask && result.mask.local) {
                      updateMask(result.mask.local);
                    } else if (detectedCountry.mask && detectedCountry.mask.local) {
                      updateMask(detectedCountry.mask.local);
                    }

                    setMessage({ text: '', type: '', action: null });
                  }
                }
              });
            }
          }
        }

        // Update the input value with the properly formatted phone number if provided
        // But ONLY if user isn't currently typing
        if (result.phone && result.phone.formatted && !isActivelyTypingRef.current) {
          const formattedValue = result.phone.formatted.local;

          // Schedule the update to occur after API processing is done
          // and don't block the main thread
          setTimeout(() => {
            setInputValue(formattedValue);

            // Call onChange callback with the formatted value
            if (onChange) {
              onChange(formattedValue);
            }

            // Also validate if the number is long enough
            if (cleanPhoneNumber(formattedValue).length >= 8) {
              setVerificationStage('ready');
              setIsNumberValid(true);

              if (onValid) {
                onValid(true);
              }
            }
          }, 0);
        }
      }
    } catch (error) {
      console.error('[PhoneInput] Error checking country:', error);
    } finally {
      // Indicate we're done processing the API response
      setProcessingAPIResponse(false);
    }
  };

  // Handle phone input changes - this is the CRITICAL function for responsive input
  const handlePhoneChange = (e) => {
    // FIRST: Mark that user is typing - do this before anything else
    handleUserTyping();

    // SECOND: Always update the input value immediately
    // This ensures the UI stays responsive no matter what
    handlePhoneInputChange(e);

    // THIRD: Clear message when input changes to avoid confusing the user
    if (message.text) {
      setMessage({ text: '', type: '', action: null });
    }

    // FOURTH: Reset verification/validation state when input changes
    if (verificationStage === 'verified') {
      setVerificationStage('idle');
    }

    if (isNumberValid) {
      setIsNumberValid(false);
    }

    // FIFTH: Clear any pending checks to avoid redundant operations
    if (countryDetectionTimeoutRef.current) {
      clearTimeout(countryDetectionTimeoutRef.current);
    }

    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }

    if (userInputTimeoutRef.current) {
      clearTimeout(userInputTimeoutRef.current);
    }

    // Country detection will happen after typing stops via the handleUserTyping timeout
    // This guarantees that typing is never blocked by API calls or country detection
  };

  // Full validation - separate from country check
  const validatePhoneNumber = async (phoneNumber) => {
    if (!phoneNumber || !selectedCountry) return;

    try {
      // Indicate we're processing an API response
      setProcessingAPIResponse(true);

      const result = await validateNumber(
        phoneNumber,
        selectedCountry.iso,
        selectedCountry.code
      );

      if (result.success) {
        // Handle country change if needed
        if (result.data && result.data.country &&
            result.data.country.iso !== selectedCountry.iso) {
          // Find the matching country in our countries list
          const detectedCountry = countries.find(
            c => c.iso === result.data.country.iso
          );

          // Instead of auto-switching, show a suggestion
          if (detectedCountry) {
            console.log('Detected different country during validation:', detectedCountry.en);

            setMessage({
              text: `Похоже, это номер из ${detectedCountry.ru || detectedCountry.en}. Переключить?`,
              type: 'info',
              action: {
                label: 'Да, переключить',
                callback: () => {
                  setSelectedCountry(detectedCountry);

                  // Update the mask based on the detected country
                  if (result.data.mask && result.data.mask.local) {
                    updateMask(result.data.mask.local);
                  } else if (detectedCountry.mask && detectedCountry.mask.local) {
                    updateMask(detectedCountry.mask.local);
                  }

                  setMessage({ text: '', type: '', action: null });
                }
              }
            });
          }
        }

        // Update the input value with the properly formatted phone number if provided
        if (result.data && result.data.phone && result.data.phone.formatted) {
          const formattedValue = result.data.phone.formatted.local;
          setInputValue(formattedValue);

          // Call onChange callback with the formatted value
          if (onChange) {
            onChange(formattedValue);
          }
        }

        setVerificationStage('ready');
        setIsNumberValid(true);

        // Call onValid callback if provided
        if (onValid) {
          onValid(true);
        }
      } else {
        setVerificationStage('idle');
        setIsNumberValid(false);

        if (result.error) {
          setMessage({
            text: result.error,
            type: 'error',
            action: null
          });

          // Call onValid callback if provided
          if (onValid) {
            onValid(false);
          }
        }
      }
    } catch (error) {
      console.error('[PhoneInput] Error validating phone:', error);
      setIsNumberValid(false);
      setMessage({
        text: 'Ошибка при проверке номера',
        type: 'error',
        action: null
      });
    } finally {
      // Indicate we're done processing the API response
      setProcessingAPIResponse(false);

      // Restore focus after validation
      restoreFocus();
    }
  };

  // Handle country selection
  const handleCountrySelect = (country) => {
    setSelectedCountry(country);

    // Update the mask based on the selected country
    if (country.mask && country.mask.local) {
      updateMask(country.mask.local);
    }

    // Re-validate phone number with new country if needed
    if (inputValue.length >= 6) {
      // Use the new checkCountryFromInput as it's more stable
      setTimeout(() => {
        checkCountryFromInput(inputValue);
      }, 100);
    }

    // Restore focus after country selection with a small delay
    setTimeout(restoreFocus, 100);
  };

  // Handle send verification code
  const handleSendCode = async () => {
    // Don't proceed if number isn't valid
    if (!isNumberValid) {
      return;
    }

    setVerificationStage('sending');
    setMessage({ text: 'Отправка кода подтверждения...', type: 'info', action: null });

    const result = await sendVerificationCode(
      inputValue,
      selectedCountry.iso,
      selectedCountry.code
    );

    if (result.success) {
      setVerificationStage('sent');
      setMessage({ text: 'Код подтверждения отправлен. Пожалуйста, введите код.', type: 'info', action: null });
    } else {
      setVerificationStage('ready');
      setMessage({ text: result.error || 'Не удалось отправить код подтверждения', type: 'error', action: null });

      // Restore focus
      restoreFocus();
    }
  };

  // Handle code verification
  const handleVerifyCode = async (code) => {
    if (code.length === 4) {
      const result = await verifyCode(code);

      if (result.success) {
        // Verify the code with the API
        setVerificationStage('verified');
        setMessage({ text: 'Код подтвержден!', type: 'success', action: null });

        // Call callbacks with the session ID
        if (onVerificationSuccess) {
          onVerificationSuccess({
            success: true,
            sessionId: result.sessionId
          });
        }

        if (onVerified) {
          onVerified();
        }
      } else {
        // Show invalid animation
        setMessage({ text: result.error || 'Неверный код подтверждения', type: 'error', action: null });
        setVerificationCode('');
      }
    }
  };

  // Handle back to phone input
  const handleBackToPhone = () => {
    setVerificationStage('ready');
    setVerificationCode('');

    // Restore focus
    restoreFocus();
  };

  // Send verification code programmatically
  const sendVerificationCodeExternal = async () => {
    if (verificationStage === 'ready') {
      await handleSendCode();
      return { success: true, sessionId };
    } else {
      return {
        success: false,
        error: 'Phone number not ready for verification'
      };
    }
  };

  // Verify code programmatically
  const verifyCodeExternal = async (code) => {
    if (verificationStage === 'sent') {
      setVerificationCode(code);
      await handleVerifyCode(code);
      return { success: true };
    } else {
      return {
        success: false,
        error: 'Verification code not sent yet'
      };
    }
  };

  // Reset the component
  const reset = () => {
    resetPhoneInput();
    setVerificationStage('idle');
    setVerificationCode('');
    setMessage({ text: '', type: '', action: null });
    setIsNumberValid(false);
    setProcessingAPIResponse(false);
    setUserTyping(false);
    isActivelyTypingRef.current = false;

    if (typingTimeoutRef.current) clearTimeout(typingTimeoutRef.current);
    if (countryDetectionTimeoutRef.current) clearTimeout(countryDetectionTimeoutRef.current);
    if (validationTimeoutRef.current) clearTimeout(validationTimeoutRef.current);
    if (userInputTimeoutRef.current) clearTimeout(userInputTimeoutRef.current);

    lastAPIRequestRef.current = { timestamp: 0, country: null, value: '' };
  };

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    sendVerificationCode: sendVerificationCodeExternal,
    validateVerificationCode: verifyCodeExternal,
    reset,
    // For compatibility with original GxPhoneInput
    reinitialize: reset
  }), [verificationStage, sessionId]);

  // Combine CSS classes
  const rootClassName = [
    'gx-phone-react-container',
    className
  ].filter(Boolean).join(' ');

  const inputWrapperClassName = [
    'gx-phone-input-wrapper',
    verificationStage === 'verified' && 'verified',
    message.type === 'error' && 'invalid',
  ].filter(Boolean).join(' ');

  // Custom arrow icon with forced white color
  const ArrowIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ color: 'white' }}>
      <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );

  return (
    <div className={rootClassName} ref={containerRef}>
      {verificationStage !== 'sent' && verificationStage !== 'verified' ? (
        <>
          <div className={inputWrapperClassName}>
            <CountrySelector
              countries={countries}
              selectedCountry={selectedCountry}
              onSelect={handleCountrySelect}
              disabled={disabled || isVerifying}
              isLoading={isCountriesLoading}
              isOpen={showCountryDropdown}
              onToggle={setShowCountryDropdown}
            />

            <input
              ref={inputRef}
              type="tel"
              value={inputValue}
              onChange={handlePhoneChange}
              className="gx-phone-input-field"
              placeholder={placeholder || (selectedCountry?.mask?.local?.replace(/X/g, '_') || "Номер телефона")}
              disabled={disabled || isVerifying}
              autoComplete="tel"
              onBlur={() => {
                isActivelyTypingRef.current = false;
                // Short delay to allow click events to process first
                setTimeout(() => {
                  if (!isActivelyTypingRef.current) {
                    restoreFocus();
                  }
                }, 100);
              }}
            />

            {verificationStage === 'ready' && requiredVerification && (
              <button
                type="button"
                className="gx-inline-button"
                onClick={handleSendCode}
                disabled={!isNumberValid || isVerifying || processingAPIResponse}
                title="Получить код подтверждения"
              >
                {isVerifying || processingAPIResponse ? (
                  <svg className="gx-spinner" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  </svg>
                ) : (
                  <ArrowIcon />
                )}
              </button>
            )}
          </div>

          {message.text && (
            <div className={`gx-message ${message.type}`}>
              {message.text}
              {message.action && (
                <button
                  type="button"
                  className="gx-message-action-button"
                  onClick={message.action.callback}
                >
                  {message.action.label}
                </button>
              )}
            </div>
          )}
        </>
      ) : (
        <VerificationInput
          ref={verificationInputRef}
          value={verificationCode}
          onChange={setVerificationCode}
          onComplete={handleVerifyCode}
          isVerified={verificationStage === 'verified'}
          onEdit={handleBackToPhone}
        />
      )}
    </div>
  );
});