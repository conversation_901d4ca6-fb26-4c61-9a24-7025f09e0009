import { ComparisonButton } from '@/features/comparison/';
import { FavoriteButton } from '@/features/favorite/';
import { useModal } from '@/features/modals/model/context';
import star from '@/shared/assets/icons/adv1fill.svg';
import share from '@/shared/assets/icons/share.svg';

const TopControls = ({ product, reviews }) => {
  const { showModal } = useModal();

  return (
    <div className="flex justify-between mb-[10px]">
      <div className="flex gap-[10px]">
        <button className="flex items-end proportional-nums  lining-nums">
          <img className="mx-auto mr-1" src={star} alt="*" />
          <span className="text-xs pt-1 mr-2 font-normal text-colBlack">
            {reviews.raiting}
          </span>
          <span className="text-xs pt-1 font-medium text-colDarkGray">
            {reviews.total_count_text}
          </span>
        </button>

        <ComparisonButton product={product} className="text-center flex flex-row justify-between items-center" />
        <FavoriteButton product={product} className="text-center flex flex-row justify-between items-center" />

        <button
          className="text-center flex flex-row justify-between items-center"
          onClick={() => showModal({ type: 'share' })}
        >
          <img className="mx-auto mr-1" src={share} alt="*" />
          <span className="text-xs pt-1 font-medium text-colBlack">
            Поделиться
          </span>
        </button>
      </div>

      {/* <div className="flex gap-[10px]">
        <button className="text-center flex flex-row justify-between items-center">
          <img className="mx-auto mr-1" src={downloadpdf} alt="*" />
          <span className="text-xs pt-1 font-medium text-colBlack">
            Скачать PDF
          </span>
        </button>
        <button className="text-center flex flex-row justify-between items-center">
          <img className="mx-auto mr-1" src={print} alt="*" />
          <span className="text-xs pt-1 font-medium text-colBlack">
            Распечатать
          </span>
        </button>
      </div> */}
    </div>
  );
};

export default TopControls;
