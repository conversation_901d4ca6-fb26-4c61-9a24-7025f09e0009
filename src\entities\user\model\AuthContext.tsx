// src/entities/user/model/AuthContext.tsx
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { hasTokenInCookies, getTokenFromCookies } from '../lib/cookies';
import { usePhoneEntryMutation } from '@/features/auth/api/authenticationApi';
import { logout as logoutAction, setToken, setUserData } from './userSlice';

import type { User } from './types';
import type { RootState } from '@/app/providers/store';

// Define the context type
export interface AuthContextType {
  isAuthenticated: boolean;
  isInitialized: boolean;
  token: string | null;
  user: User | null;
  logout: () => void;
  authenticateWithPhone: (sessionId: string, additionalData?: Record<string, unknown>) => Promise<{
    success: boolean;
    user?: User;
    token?: string;
    error?: string;
  }>;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType | null>(null);

// Provider component
export const AuthProvider: React.FC<React.PropsWithChildren<{}>> = ({ children }) => {
  const dispatch = useDispatch();
  const {
    token,
    data: user,
    isInitialized,
  } = useSelector((state: RootState) => state.user);

  // Direct cookie check for more reliable authentication status
  const isAuthenticated = hasTokenInCookies();

  // Get the phone entry mutation
  const [phoneEntryMutation] = usePhoneEntryMutation();

  // Simple logout function that clears auth state
  const logout = useCallback(() => {
    // Dispatch the logout action to clear auth state
    dispatch(logoutAction());
  }, [dispatch]);

  // Enhanced authenticate with phone that also syncs user data
  const authenticateWithPhone = useCallback(async (
    sessionId: string,
    additionalData: Record<string, unknown> = {}
  ) => {
    try {
      const entryResponse = await phoneEntryMutation({
        session: sessionId,
        ...additionalData
      }).unwrap();

      if (entryResponse.token) {
        dispatch(setToken({
          token: entryResponse.token,
          source: 'furnica',
        }));
      }

      if (entryResponse.user) {
        const userData: User = {
          id: parseInt(entryResponse.user.id, 10) || 0,
          name: entryResponse.user.name || '',
          phone: entryResponse.user.phone,
          email: '',
          photo: null,
          gender: '',
          date_of_birth: null,
        };

        dispatch(setUserData(userData));
      }

      // Note: Data sync will be handled by a separate hook after authentication

      let userData: User | undefined = undefined;
      if (entryResponse.user) {
        userData = {
          id: parseInt(entryResponse.user.id, 10) || 0,
          name: entryResponse.user.name || '',
          phone: entryResponse.user.phone,
          email: '',
          photo: null,
          gender: '',
          date_of_birth: null,
        };
      }

      return {
        success: true,
        user: userData,
        token: entryResponse.token,
      };
    } catch (error) {
      console.error('[AuthContext] Authentication error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ошибка авторизации',
      };
    }
  }, [dispatch, phoneEntryMutation]);

  // Create the context value
  const contextValue: AuthContextType = {
    isAuthenticated,
    isInitialized,
    token,
    user,
    logout,
    authenticateWithPhone,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};
