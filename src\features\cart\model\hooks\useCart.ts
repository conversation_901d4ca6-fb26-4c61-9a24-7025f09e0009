import { useEffect, useCallback, useRef } from 'react';

import { useSelector, useDispatch } from 'react-redux';

import { useAuthContext } from '@/entities/user/model/AuthContext';
import {
  useGetUserCartQuery,
  useSendCartMutation,
  setCart,
} from '@/features/cart';
import {
  saveToSessionStorage,
  getFromSessionStorage,
} from '@/features/storage/lib';

import type { RootState } from '@/app/providers/store';
import type { LocalCartState } from '@/features/cart';

// Initial empty cart state for resets
const initialCartState: LocalCartState = {
  cart: [],
  total: {
    items_count: 0,
    quantity: 0,
    price_before_discount: 0,
    discount: 0,
    price_after_discount: 0,
  },
  selected: {
    items_count: 0,
    quantity: 0,
    price_before_discount: 0,
    discount: 0,
    price_after_discount: 0,
  },
  currency: {
    code: 'RUB',
    title: 'Рубль',
    symbol: '₽',
  },
};

/**
 * Enhanced hook for accessing and managing cart data throughout the application.
 * This is the single source of truth for cart data.
 *
 * Features:
 * - <PERSON>les both authenticated and unauthenticated states
 * - Provides access to cart data from Redux store
 * - Manages cart data fetching with optimized caching
 * - Provides helper methods for finding items in cart
 * - Includes methods for syncing with server and storage
 */
export const useCart = () => {
  const { isAuthenticated } = useAuthContext();
  const localCart = useSelector((state: RootState) => state.cart);
  const dispatch = useDispatch();
  const hasLoadedFromStorageRef = useRef(false);

  // Only fetch cart data from server on initial load or when explicitly needed
  // The skip parameter ensures we don't fetch if not authenticated
  const {
    isLoading,
    error,
    refetch: refetchCart,
  } = useGetUserCartQuery(undefined, {
    skip: !isAuthenticated,
    // Reduce refetching frequency
    refetchOnMountOrArgChange: false,
    refetchOnFocus: false,
    refetchOnReconnect: false,
  });

  // API mutation hook for sending cart updates to server
  const [sendCart] = useSendCartMutation();

  // Helper function to find a product in the cart by ID
  const findCartItem = useCallback(
    (productId: number) => {
      return localCart?.cart?.find((item) => item.id === productId) || null;
    },
    [localCart?.cart]
  );

  // Helper function to check if a product is in the cart
  const isInCart = useCallback(
    (productId: number) => {
      return !!findCartItem(productId);
    },
    [findCartItem]
  );

  // Force refresh cart data from server (use sparingly)
  const refreshFromServer = useCallback(async () => {
    if (isAuthenticated) {
      try {
        const result = await refetchCart();

        if (result.data) {
          // Transform server data to local format and update Redux state
          const transformedData = {
            cart: result.data.data,
            total: result.data.total,
            selected: result.data.selected,
            currency: result.data.current_currency,
          };
          dispatch(setCart(transformedData));
        }

        return result;
      } catch (error) {
        console.error('[useCart] Error fetching from server:', error);
        throw error;
      }
    }
    return Promise.resolve({ data: localCart });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, refetchCart]); // dispatch is stable, localCart causes re-renders

  // Send current cart to server
  const syncToServer = useCallback(
    async (cartItems = localCart?.cart) => {
      if (!isAuthenticated || !cartItems?.length) {
        return Promise.resolve();
      }

      try {
        const payload = cartItems.map((item) => ({
          id: item.id,
          quantity: item.quantity,
          selected: !!item.selected, // Convert to boolean
        }));

        return await sendCart({ items: payload });
      } catch (error) {
        console.error('[useCart] Error syncing cart to server:', error);
        return Promise.reject(error);
      }
    },
    [isAuthenticated, localCart?.cart, sendCart]
  );

  // Load cart data from sessionStorage
  const loadFromStorage = useCallback(() => {
    try {
      const storedCart = getFromSessionStorage('cart') as LocalCartState | null;
      if (storedCart) {
        dispatch(setCart(storedCart));
      }
      hasLoadedFromStorageRef.current = true;
    } catch (error) {
      console.error('[useCart] Error loading from storage:', error);
    }
  }, [dispatch]);

  // Save cart data to sessionStorage
  const saveToStorage = useCallback(
    (cartData = localCart) => {
      try {
        if (!isAuthenticated && cartData) {
          saveToSessionStorage('cart', cartData);
        }
      } catch (error) {
        console.error('[useCart] Error saving to storage:', error);
      }
    },
    [isAuthenticated] // Remove localCart from dependencies to prevent infinite loop
  );

  // Clear cart data in Redux and sessionStorage
  const clearData = useCallback(() => {
    dispatch(setCart(initialCartState));
    sessionStorage.removeItem('cart');
  }, [dispatch]);

  // Auto-save to storage when cart changes (for non-authenticated users)
  // Only save after initial load to prevent overwriting stored data
  useEffect(() => {
    // Only auto-save if:
    // 1. Not authenticated (local storage mode)
    // 2. Has loaded from storage at least once (prevents overwriting on initial load)
    // 3. Has cart data
    if (!isAuthenticated && hasLoadedFromStorageRef.current && localCart) {
      saveToStorage(localCart);
    }
  }, [localCart, isAuthenticated, saveToStorage]);

  // Log cart state changes for debugging
  useEffect(() => {
    console.log('[useCart] Cart state updated:', {
      itemCount: localCart?.cart?.length,
      totalQuantity: localCart?.total?.quantity,
      isAuthenticated,
    });
  }, [localCart, isAuthenticated]);

  return {
    cart: localCart,
    isLoading,
    isError: !!error,
    findCartItem,
    isInCart,
    refreshCart: refreshFromServer, // Keep the original name for backward compatibility
    refreshFromServer,
    syncToServer,
    loadFromStorage,
    saveToStorage,
    clearData,
    initialCartState,
  };
};
