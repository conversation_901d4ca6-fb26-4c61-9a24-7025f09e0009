import { Breadcrumbs } from '@/widgets/breadcrumbs';
import {
  ProductCard,
  ProductCardLine,
  ProductCardLineSkeleton,
  ProductCardSkeleton
} from '@/widgets/product-card';
import StickyBox from 'react-sticky-box';
import { CatalogChipses } from './CatalogChipses';
import CardTypeControls from './CatalogContent/CardTypeControls';
import { CatalogSidebarFilters } from './CatalogSidebarFilters';
import { CatalogSort } from './CatalogSort';
import { useCatalogTest } from './hooks/useCatalogTest';

export const CatalogTest = () => {
  const {
    filters,
    onChangeFilter,
    filtersLoading,
    handleOpenModalAllFilters,
    onResetFilters,
    chips,
    currentSort,
    handleSortChange,
    sortBy,
    cardType,
    setTypeCard,
    isLoadingProducts,
    products,
    hasMore,
    loadMoreRef,
    categoryTree,
    prevCategories
  } = useCatalogTest();

  return (
    <div className='content lining-nums proportional-nums p-2'>
      <Breadcrumbs />

      <div className='flex gap-[20px] items-start'>
        <StickyBox offsetTop={90} offsetBottom={20}>
          <CatalogSidebarFilters
            prevCategories={prevCategories}
            categories={categoryTree}
            filters={filters}
            onChangeFilter={onChangeFilter}
            filtersLoading={filtersLoading}
            handleOpenModalAllFilters={handleOpenModalAllFilters}
            onResetFilters={onResetFilters}
          />
        </StickyBox>
        <div className='flex flex-col gap-[24px] w-full'>
          <div className='flex flex-col gap-[20px]'>
            <div className='flex justify-between'>
              <CatalogSort
                currentSort={currentSort}
                handleSortChange={handleSortChange}
                sortBy={sortBy}
              />
              <CardTypeControls cardType={cardType} setTypeCard={setTypeCard} />
            </div>
            <CatalogChipses
              chips={chips}
              filtersLoading={filtersLoading}
              onChangeFilter={onChangeFilter}
              onResetFilters={onResetFilters}
            />
          </div>

          {cardType === 'tile' ? (
            <div className='grid grid-cols-2 mm:grid-cols-3 md:grid-cols-2 lg:grid-cols-3 ll:grid-cols-4 gap-3 gap-y-6 xl:grid-cols-5'>
              {!!products.length
                ? products.map((el) => <ProductCard key={el?.id} product={el} />)
                : null}

              {isLoadingProducts
                ? Array.from({ length: 20 }).map((_, index) => <ProductCardSkeleton key={index} />)
                : null}
            </div>
          ) : null}
          {cardType === 'line' ? (
            <div className='space-y-4'>
              {isLoadingProducts
                ? Array.from({ length: 20 }).map((_, index) => (
                    <ProductCardLineSkeleton key={index} />
                  ))
                : null}
              {!!products.length &&
                products.map((el) => <ProductCardLine key={el?.id} product={el} />)}
            </div>
          ) : null}
          {isLoadingProducts && <div className='text-center'>Идет загрузка...</div>}
          {hasMore && <div ref={loadMoreRef} style={{ height: '20px' }} />}
          {!hasMore && <div>Больше нет товаров</div>}
        </div>
      </div>
    </div>
  );
};
