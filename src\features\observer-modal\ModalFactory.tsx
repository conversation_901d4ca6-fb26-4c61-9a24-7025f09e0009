import { ModalFilterData, ObserverItem } from '@/shared/types/observer';
import { ModalAllFilter } from './ModalAllFilter';
import { ModalCategoryFilter } from './ModalCategoryFilter';
import { ModalItem } from './ModalItem';
interface ModalFactoryProps {
  data: ObserverItem<ModalFilterData>[];
}

export const ModalFactory = ({ data }: ModalFactoryProps) => {
  return data.map((item) => {
    switch (item.type) {
      case 'item': {
        return <ModalItem data={item} key={item.observerId} />;
      }
      case 'all': {
        return <ModalAllFilter data={item} key={item.observerId} />;
      }
      case 'category': {
        return <ModalCategoryFilter data={item} key={item.observerId} />;
      }
    }
  });
};
