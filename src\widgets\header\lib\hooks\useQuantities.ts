import { useSelector } from 'react-redux';

import type { RootState } from '@/app/providers/store';
import { useCart } from '@/features/cart';

export const useQuantities = () => {
  const favorite = useSelector((state: RootState) => state.favorite.favorite);
  const comparison = useSelector(
    (state: RootState) => state.comparison.comparison
  );
  // Use the standardized cart hook
  const { cart } = useCart();

  const getFavoritesCount = () => {
    return favorite?.length || 0;
  };

  const getComparisonCount = () => {
    return comparison?.length || 0;
  };

  const getCartQuantity = () => {
    // Always calculate the total quantity from cart items to ensure accuracy
    // This prevents issues when items are removed from the cart
    return cart.cart?.reduce((total, item) => total + (item.quantity || 0), 0) || 0;
  };

  return {
    getFavoritesCount,
    getComparisonCount,
    getCartQuantity,
  };
};
