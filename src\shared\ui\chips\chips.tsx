import { CloseCircle } from '../icons';

interface ChipsProps {
  title: string;
  text?: string;
  onClick: () => void;
  disabled?: boolean;
  className?: string;
  variant?: 'primary' | 'secondary';
}

export const Chips: React.FC<ChipsProps> = ({
  title,
  text,
  onClick,
  disabled,
  className,
  variant = 'primary'
}) => {
  return (
    <div
      className={` rounded-[8px] py-[6px] px-[14px] flex justify-center items-center w-fit gap-[8px] lining-nums ${className} ${variant === 'primary' ? 'bg-colGreen  text-[white]' : 'bg-[#E6E9E8] text-[#000000]'}`}
    >
      <span>
        {title}
        {text && `: ${text}`}
      </span>
      <button onClick={onClick} disabled={disabled}>
        <CloseCircle />
      </button>
    </div>
  );
};
