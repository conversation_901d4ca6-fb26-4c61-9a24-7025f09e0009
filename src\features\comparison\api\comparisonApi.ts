// src/redux/api/comparisonEndpoints.js
import { api } from '@/shared/api/api';

import type { AdditionalServerResponseData } from '@/shared/types/AdditionalServerResponseData';
import type { ProductListRequest } from '@/shared/types/ProductListRequest';
import type { GetComparisonResponse } from '@/types/ServerData/Comparison/GetComparison';

export const comparisonEndpoints = api.injectEndpoints({
  endpoints: (builder) => ({
    getComparison: builder.query<GetComparisonResponse, void>({
      query: () => '/api/ProductsComparisons/get',
      providesTags: [{ type: 'Comparison', id: 'LIST' }],
    }),
    sendComparison: builder.mutation<
      AdditionalServerResponseData,
      ProductListRequest
    >({
      query: (data) => ({
        url: '/api/ProductsComparisons/set',
        method: 'POST',
        body: data,
      }),
      // Don't invalidate any tags to prevent automatic refetching
      invalidatesTags: () => [],
      // Instead, manually update the cache
      async onQueryStarted(_arg, { queryFulfilled }) {
        try {
          await queryFulfilled;
          // No need to update the cache here - the Redux store is already updated
          // by the comparison slice, and components read from there
        } catch {
          // Error handling is done by the API layer
        }
      },
    }),
    removeFromComparison: builder.mutation<
      AdditionalServerResponseData,
      ProductListRequest
    >({
      query: (data) => ({
        url: '/api/ProductsComparisons/delete',
        method: 'POST',
        body: data,
      }),
      // Don't invalidate any tags to prevent automatic refetching
      invalidatesTags: () => [],
      // Instead, manually update the cache
      async onQueryStarted(_arg, { queryFulfilled }) {
        try {
          await queryFulfilled;
          // No need to update the cache here - the Redux store is already updated
          // by the comparison slice, and components read from there
        } catch {
          // Error handling is done by the API layer
        }
      },
    }),
  }),
});

// Export hooks for comparison endpoints
export const {
  useGetComparisonQuery,
  useSendComparisonMutation,
  useRemoveFromComparisonMutation,
} = comparisonEndpoints;
