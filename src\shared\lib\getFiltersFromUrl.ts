import queryString from 'query-string';
import { removePrefixes } from './removePrefixes';

export const getFiltersFromUrl = () => {
  const params = queryString.parse(window.location.search, {
    arrayFormat: 'separator',
    arrayFormatSeparator: '|'
  });
  const filterParams = removePrefixes(params, ['filter_', 'range_', 'sort_']);
  const filters = Object.entries(filterParams).reduce((acc, [key, value]) => {
    return { ...acc, [key]: value.value };
  }, {});

  return { filterParams, filters };
};
