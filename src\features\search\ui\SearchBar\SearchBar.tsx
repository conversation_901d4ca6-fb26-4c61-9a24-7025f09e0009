import { useEffect, useState, useRef } from 'react';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';

import { useSearchSuggestions, useSearchCorrections } from '@/features/search/lib/hooks';
import search from '@/shared/assets/icons/search.svg';
import noImg from '@/shared/assets/images/no-image.png';
import { SearchCorrections } from './SearchCorrections';
import type { SearchCorrectionItem } from '../../api/types';

export interface SearchBarProps {
  setShowCatalog: (show: boolean) => void;
}

export const SearchBar = ({ setShowCatalog }: SearchBarProps) => {
  const { 
    searchTerm,
    setSearchTerm,
    suggestions,
    isLoading,
    getSuggestions 
  } = useSearchSuggestions();

  // Use the search corrections hook
  const {
    corrections,
    applyCorrection,
  } = useSearchCorrections(searchTerm);

  const [isFocused, setIsFocused] = useState(false);
  const searchFormRef = useRef<HTMLDivElement>(null);
  const hasCorrections = isFocused && searchTerm.length >= 2 && corrections && corrections.length > 0;

  const handleFocus = () => {
    setIsFocused(true);
    setShowCatalog(false);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const navigate = useNavigate();
  const location = useLocation();

  // Extract search query from URL
  useEffect(() => {
    if (location.pathname === '/search') {
      const searchParams = new URLSearchParams(location.search);
      setSearchTerm(searchParams.get('q') || '');
    }
  }, [location, setSearchTerm]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowCatalog(false);
    handleBlur();
    e.currentTarget.blur();

    // Don't navigate if search is empty
    if (!searchTerm.trim()) return;

    // Navigate to the search page with the search query
    navigate(`/search?q=${encodeURIComponent(searchTerm.trim())}`);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFocus();
    setSearchTerm(e.target.value);
  };

  // Handle correction selection
  const handleCorrectionSelect = (item: SearchCorrectionItem) => {
    const newSearchTerm = applyCorrection(item, searchTerm);
    setSearchTerm(newSearchTerm);
    
    // Re-trigger suggestions with the new term
    getSuggestions(newSearchTerm);
  };

  return (
    <div className="w-full mx-2">
      {isFocused && (
        <div
          className="fixed h-[100vh] w-[100vw] left-0 top-0 bg-black bg-opacity-50 z-20"
          onClick={handleBlur}
        ></div>
      )}
      
      <div ref={searchFormRef} className={`flex flex-col z-30 bg-white relative  p-2  ${hasCorrections ? 'rounded-t-md' : 'rounded-md'}`}>
        <form
          onSubmit={handleSearchSubmit}
          className="z-20 w-full border-colGreen border rounded-lg flex justify-between"
        >
          <input
            className="w-full h-[34px] mm:h-10 outline-none rounded-l-md bg-white px-3 border-none lining-nums proportional-nums"
            type="search"
            placeholder="Поиск по сайту"
            onFocus={handleFocus}
            onChange={handleInputChange}
            value={searchTerm}
          />
          <button type="submit" className="bg-colGreen rounded-r-md w-14">
            <img className="mx-auto" src={search} alt="*" />
          </button>
        </form>

        {/* Main dropdown container for both corrections and suggestions */}
        <div className={`absolute left-0 right-0 top-full z-40`} 
             style={{ display: (isFocused && (hasCorrections || suggestions)) ? 'block' : 'none' }}>
          
          {/* Corrections section with padding */}
          {hasCorrections && (
            <div className="p-2 rounded-b-md  bg-white">
              <SearchCorrections 
                corrections={corrections} 
                onSelect={handleCorrectionSelect}
                className="gap-3"
              />
            </div>
          )}
          
          {/* Gap between corrections and suggestions */}
          {hasCorrections && suggestions && <div className="h-2"></div>}
          
          {/* Suggestions section */}
          {isFocused && suggestions && (
            <div className="relative w-full max-h-[50vh] overflow-auto proportional-nums lining-nums bg-white rounded-md">
              {/* History section */}
              {suggestions?.history?.length > 0 && (
                <div className="py-2 px-4 text-sm font-medium text-gray-500">Недавние поисковые запросы</div>
              )}
              
              {suggestions?.history?.map((result: any, index: number) => (
                <li
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  key={`history-${index}`}
                  className="px-4 py-2 hover:bg-gray-100"
                >
                  <NavLink
                    to={`/search?q=${encodeURIComponent(result.text)}`}
                    onClick={handleBlur}
                    className="flex items-center"
                  >
                    <span className="text-gray-400 mr-3">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M21 21L16.65 16.65" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </span>
                    <div className="font-medium">{result.text}</div>
                  </NavLink>
                </li>
              ))}
              
              {/* Product variants section */}
              {suggestions?.variants?.length > 0 && (
                <div className="py-2 px-4 text-sm font-medium text-gray-500">Товары</div>
              )}
              
              {suggestions?.variants?.map((result: any, index: number) => (
                <li
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  key={`variant-${index}`}
                  className="px-4 py-2 hover:bg-gray-100"
                >
                  <NavLink
                    to={`/catalog/${result.categorySlug}/${result.slug}`}
                    onClick={handleBlur}
                  >
                    <div className="flex items-center gap-3">
                      <img
                        src={result.files?.small || noImg}
                        alt=""
                        className="w-10 h-10 rounded-md object-contain"
                      />
                      <div>
                        <div>
                          {result.groupName} {result.name}
                        </div>
                        <div className="text-colDarkGray text-xs">
                          {result.article}
                        </div>
                      </div>
                    </div>
                  </NavLink>
                </li>
              ))}

              {/* Categories section */}
              {suggestions?.categories?.length > 0 && (
                <div className="py-2 px-4 text-sm font-medium text-gray-500">Категории</div>
              )}
              
              {suggestions?.categories?.map((result: any, index: number) => (
                <li
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  key={`category-${index}`}
                  className="px-4 py-2 hover:bg-gray-100"
                >
                  <NavLink to={`/catalog/${result.slug}`} onClick={handleBlur}>
                    <div className="flex items-center gap-3">
                      <div>
                        <div className="text-xs text-colDarkGray">
                          {result.chain?.slice(0, -1)?.map((cat: any) => `${cat.name}/`)}
                          <div className="text-sm text-colBlack">
                            {result.name}
                          </div>
                        </div>
                        <div className="text-colDarkGray text-xs">
                          Категория
                        </div>
                      </div>
                    </div>
                  </NavLink>
                </li>
              ))}
              
              {/* No results message */}
              {!isLoading && 
                searchTerm.length > 0 && 
                !suggestions?.variants?.length && 
                !suggestions?.categories?.length && 
                !suggestions?.history?.length && (
                <div className="p-4 text-center text-gray-500">
                  Ничего не найдено. Попробуйте другой запрос.
                </div>
              )}
              
              {/* See all results link */}
              {(suggestions?.variants?.length > 0 || suggestions?.categories?.length > 0) && (
                <li className="px-4 py-3 hover:bg-gray-100 border-t">
                  <NavLink
                    to={`/search?q=${encodeURIComponent(searchTerm)}`}
                    onClick={handleBlur}
                    className="text-colGreen font-medium block text-center"
                  >
                    Посмотреть все результаты
                  </NavLink>
                </li>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
