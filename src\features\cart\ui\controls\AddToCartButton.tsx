// src/features/cart/ui/controls/AddToCartButton.tsx
import { Button } from '@/shared/ui';
import { useAddToCart, type AddToCartButtonStateType } from '@/features/cart';

import type { Product } from '@/entities/product';
import type { ButtonProps } from '@/shared/ui/button';

import { Icon } from '@iconify-icon/react';

type AddToCartButtonProps = {
  product: Product;
} & Omit<ButtonProps, 'onClick' | 'disabled' | 'isLoading' | 'children'>;

export const AddToCartButton = ({
  product,
  className = '',
  variant = 'primary',
  size = 'md',
  fullWidth = true,
  asChild = false,
  ...props
}: AddToCartButtonProps) => {
  const { handleAddToCartClick, buttonState } = useAddToCart(product);

  // Function to get the appropriate icon based on button state
  const getButtonIcon = () => {
    switch (buttonState.stateType) {
      case 'outOfStock':
        return <Icon icon="solar:cart-cross-linear" width="24" height="24" className="text-colText" />;
      case 'noPrice':
        return <Icon icon="solar:info-circle-linear" width="24" height="24" className="text-yellow-500" />;
      case 'preorder':
        return <Icon icon="solar:clock-circle-linear" width="24" height="24" className="text-blue-500" />;
      case 'loading':
        // return <Icon icon="solar:refresh-circle-linear" width="24" height="24" className="animate-spin" />;
        return null;
      case 'normal':
      default:
        return <Icon icon="solar:cart-large-2-linear" width="24" height="24" />;
    }
  };

  // Determine button background color based on state
  const getButtonClass = () => {
    switch (buttonState.stateType) {
      case 'outOfStock':
        return 'bg-colDarkGray';
      case 'noPrice':
        return 'bg-yellow-700';
      case 'preorder':
        return 'bg-blue-700';
      default:
        return buttonState.disabled ? 'bg-colDarkGray' : '';
    }
  };

  return (
    <Button
      onClick={handleAddToCartClick}
      disabled={buttonState.disabled}
      isLoading={buttonState.loading}
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      asChild={asChild}
      className={`${getButtonClass()} ${className}`}
      {...props}
    >
      {getButtonIcon()}
      {buttonState.text}
    </Button>
  );
};