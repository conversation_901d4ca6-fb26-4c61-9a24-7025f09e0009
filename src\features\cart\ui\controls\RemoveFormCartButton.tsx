import { useRemoveFromCart } from '@/features/cart';
import type { Product } from '@/entities/product';
import { Icon } from '@iconify-icon/react';
interface RemoveFromCartButtonProps {
  product: Product;
  withConfirmation?: boolean;
  className?: string;
}

export const RemoveFromCartButton = ({
  product,
  withConfirmation = false,
  className = '',
}: RemoveFromCartButtonProps) => {
  const { handleRemove, isLoading } = useRemoveFromCart(product, {
    withConfirmation,
  });

  // return (
  //   <button
  //     onClick={handleRemove}
  //     disabled={isLoading}
  //     className={`transition-all duration-300 hover:scale-110 ${
  //       isLoading ? 'cursor-wait' : 'cursor-pointer'
  //     } ${className}`}
  //   >
  //     <DeleteIcon />
  //   </button>

return (
  <button
      onClick={handleRemove}
      disabled={isLoading}
      className={`transition-all duration-300 hover:scale-110 ${isLoading ? 'cursor-wait' : 'cursor-pointer'} ${className}`}
    >
          <Icon icon="solar:trash-bin-minimalistic-linear"
          width={24}
          height={24}
          className="text-colGreen" />

    </button>
  );
};
