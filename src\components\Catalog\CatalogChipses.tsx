import { Chips } from '@/shared/ui';

interface CatalogChipsesProps {
  filtersLoading: boolean;
  chips: any[];
  onChangeFilter: (payload: { alone: any }) => void;
  onResetFilters: () => void;
}

export const CatalogChipses: React.FC<CatalogChipsesProps> = ({
  chips,
  filtersLoading,
  onChangeFilter,
  onResetFilters
}) => {
  if (!chips.length) {
    return null;
  }

  return (
    <div className='flex flex-wrap gap-[10px]'>
      {chips.map((item, index) => (
        <Chips
          title={item.title}
          text={item.type === 'multiple' ? item.text : `от ${item.value[0]} до ${item.value[1]}`}
          onClick={() => {
            if (item.type === 'range') {
              onChangeFilter({ alone: { ...item, value: null } });
            } else {
              onChangeFilter({ alone: item });
            }
          }}
          key={`${item.parentId}-${index}`}
          disabled={filtersLoading}
        />
      ))}
      <Chips
        title={'Очистить фильтр'}
        onClick={onResetFilters}
        disabled={filtersLoading}
        variant='secondary'
      />
    </div>
  );
};
