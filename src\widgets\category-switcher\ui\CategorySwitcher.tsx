import { memo } from 'react';
import clsx from 'clsx';
import { ProductListCategoryChain } from '@/entities/category';
import categoryIcon from '@/shared/assets/icons/category.svg';

interface CategorySwitcherProps {
  categories: ProductListCategoryChain[];
  selectedCategory: string | number;
  onCategoryChange: (categoryId: string | number) => void;
}

export const CategorySwitcher = memo(({
  categories = [],
  selectedCategory,
  onCategoryChange,
}: CategorySwitcherProps) => {
  // Defensive check to ensure categories is always an array
  const safeCategories = Array.isArray(categories) ? categories : [];
  
  // Return null if there are no categories to display
  if (!safeCategories.length) return null;

  const totalCount = safeCategories.reduce((sum, cat) => sum + (cat.count || 0), 0);

  return (
    <div className="overflow-x-scroll md:overflow-x-hidden scrollable flex py-3 md:grid md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-7 gap-3 pr-2">
      <button
        className={clsx(
          'shadow-[0_1px_2px_0_rgba(0,0,0,.1)] p-1 lg:p-2 rounded-md',
          'flex justify-center items-center outline-none min-w-[120px]',
          'transition-colors duration-200',
          selectedCategory === '' ? 'bg-gray-200' : 'bg-white'
        )}
        onClick={() => onCategoryChange('')}
      >
        <img className="w-4 mr-1" src={categoryIcon} alt="All categories" />
        Все
      </button>

      {safeCategories.map((el) => {
        // Check if chain exists and has items
        if (!el.chain || !Array.isArray(el.chain) || !el.chain.length) {
          return null;
        }
        
        const lastCategory = el.chain[el.chain.length - 1];
        if (!lastCategory) {
          return null;
        }
        
        return (
          <button
            key={lastCategory.id}
            className={clsx(
              'shadow-[0_1px_2px_0_rgba(0,0,0,.1)] p-1 lg:p-2 rounded-md',
              'flex justify-center items-center outline-none min-w-[120px]',
              'transition-colors duration-200',
              selectedCategory === lastCategory.id ? 'bg-gray-200' : 'bg-white'
            )}
            onClick={() => onCategoryChange(lastCategory.id)}
          >
            {lastCategory.image && (
              <img 
                className="w-4 h-4 mr-1" 
                src={typeof lastCategory.image === 'object' && lastCategory.image !== null 
                  ? lastCategory.image.medium || lastCategory.image.small 
                  : categoryIcon} 
                alt={lastCategory.name} 
              />
            )}
            <span className="truncate text-sm">{lastCategory.name || 'Категория'}</span>
            <span className="text-xs text-gray-500 ml-1">({el.count || 0})</span>
          </button>
        );
      })}
    </div>
  );
});

CategorySwitcher.displayName = 'CategorySwitcher';
