import { createBrowserRouter, createRoutesFromElements, Route } from 'react-router-dom';

import { Layout } from '@/app/layouts/Layout';
import BrandProducts from '@/components/Catalog/Brands';
import { CatalogRoot } from '@/components/Catalog/CatalogRoot';
import { CatalogTest } from '@/components/Catalog/CatalogTest';
import SearchProducts from '@/components/Catalog/Search';
import TagProducts from '@/components/Catalog/Tags';
import ChangePassword from '@/components/Profile/ChangePassword/ChangePassword';
import MyOrders from '@/components/Profile/MyOrders/MyOrders';
import Organizations from '@/components/Profile/Organizations/Organizations';
import PersonalData from '@/components/Profile/PersonalData/PersonalData';
import UserReviews from '@/components/Profile/UserReviews/UserReviews';
import ProtectedRoute from '@/components/ProtectedRoute/ProtectedRoute';
import About from '@/pages/About/About';
import { CartPage } from '@/pages/cart/CartPage';
// Import our new checkout page
import { CheckoutPage } from '@/pages/Checkout/components';
import Comparison from '@/pages/Comparison/Comparison';
import Contacts from '@/pages/Contacts/Contacts';
import FAQ from '@/pages/FAQ/FAQ';
import Favorites from '@/pages/Favorites/Favorites';
import Home from '@/pages/Home/Home';
import PageNotFound from '@/pages/PageNotFound/PageNotFound';
import PaymentDelivery from '@/pages/PaymentDelivery/PaymentDelivery';
import ProductPage from '@/pages/ProductPage/ProductPage';
import Profile from '@/pages/Profile/Profile';
import Wallet from '@/pages/Profile/Wallet';
import ReviewsPage from '@/pages/Reviews/ReviewsPage';
import Warranty from '@/pages/Warranty/Warranty';
import Wholesale from '@/pages/Wholesale/Wholesale';

export const router = createBrowserRouter(
  createRoutesFromElements(
    <Route>
      <Route path='/' element={<Layout />}>
        <Route index element={<Home />} />
        <Route path='shopping-cart' element={<CartPage />} />
        {/* Use our new CheckoutPage component */}
        {/* <Route path='checkout' element={<CheckoutPage />} /> */}
        <Route path='favorites' element={<Favorites />} />
        <Route path='comparison' element={<Comparison />} />

        {/* Search routes */}
        <Route path='search' element={<SearchProducts />} />

        {/* Catalog routes - Category pages */}
        <Route path='catalog'>
          <Route index element={<CatalogRoot />} />
          <Route path=':categoryId'>
            <Route
              index
              element={
                <>
                  {/* <Catalog /> */}
                  <CatalogTest />
                </>
              }
            />
            <Route path=':productId'>
              <Route index element={<ProductPage />} />
              <Route path='reviews' element={<ReviewsPage />} />
            </Route>
          </Route>
        </Route>

        {/* Brand pages - Query parameter approach */}
        <Route path='brands' element={<BrandProducts />}>
          {/* No nested routes here to avoid conflict */}
        </Route>

        {/* Tag pages - Query parameter approach */}
        <Route path='tags' element={<TagProducts />}>
          {/* No nested routes here to avoid conflict */}
        </Route>
        <Route path='profile/orders/:orderId' element={<CheckoutPage />} />

        <Route
          path='profile'
          element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          }
        >
          <Route path='personal-data' element={<PersonalData />} />
          <Route path='organizations' element={<Organizations />} />
          <Route path='change-password' element={<ChangePassword />} />
          <Route path='orders'>
            <Route index element={<MyOrders />} />
          </Route>
          <Route path='wallet' element={<Wallet />} />
          <Route path='user-reviews' element={<UserReviews />} />
        </Route>
        <Route path='payment-delivery' element={<PaymentDelivery />} />
        <Route path='warranty' element={<Warranty />} />
        <Route path='wholesale' element={<Wholesale />} />
        <Route path='contacts' element={<Contacts />} />
        <Route path='about' element={<About />} />
        <Route path='faq' element={<FAQ />} />
        <Route path='*' element={<PageNotFound />} />
      </Route>
    </Route>
  )
);
