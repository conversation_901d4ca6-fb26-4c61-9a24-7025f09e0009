import { NavLink } from 'react-router-dom';

import { QuantityControl, RemoveFromCartButton } from '@/features/cart';
import { FavoriteButton } from '@/features/favorite';
import { ProductTags } from '@/entities/product';
import { CCheckBoxField } from '@/shared/ui/inputs';
import {
  PreviewGallery,
  PriceDisplay,
  ProductAttributesDisplay,
  DiscountBadge,
  StockStatus,
  ProductName
} from '@/widgets/product-card';

import { DeliveryInfo } from './DeliveryInfo';

import { useCartSelection } from '@/features/cart/model/hooks/useCartSelection';

import type { CartProduct } from '@/features/cart/model/types';

type CartItemProps = {
  product: CartProduct;
};

export const CartItem = ({ product }: CartItemProps): JSX.Element => {
  const { selectedItems, isUpdating, handleItemSelection } = useCartSelection();
  const isSelected = selectedItems.some((item) => item.id === product.id);

  // Calculate the total price based on quantity
  const totalPrice = product.price?.final
    ? product.price.final * product.quantity
    : 0;

  return (
    <div className="flex gap-4 border-t border-b border-[#EBEBEB] py-4 bg-white rounded-lg">
      {/* Checkbox and Product Image */}
      <div className="flex items-start">
        <div className="mt-1 mr-2">
          <CCheckBoxField
            label=""
            styles=""
            checked={isSelected}
            onChange={() => handleItemSelection(product, !isSelected)}
            disabled={isUpdating}
          />
        </div>
        <div className="relative">
          <NavLink to={`/catalog/${product?.category?.slug}/${product?.slug}`}>
            <PreviewGallery
              product={product}
              showButtons={false}
              showTags={false}
              className="max-w-[180px] min-w-[180px] h-[180px] overflow-hidden relative bg-gray-100 rounded-lg"
            />
          </NavLink>


        </div>
      </div>

      {/* Product Details */}
      <div className="flex flex-col justify-between overflow-hidden grow">
        <div className="flex flex-col gap-2">

{/* Discount Badge - Positioned on the image */}
            <DiscountBadge product={product} />

          {/* Stock Status - Positioned on the image */}
            <StockStatus product={product} />
          {/* Product Name */}
          <ProductName
            product={product}
            maxLines={2}
            size="sm"
            weight="semibold"
            className="mt-1"
          />
          {/* Product Tags */}
          {product?.tags && Array.isArray(product.tags) && product.tags.length > 0 && (
            <div className="my-1">
              <ProductTags product={product} variant="default" limit={3} />
            </div>
          )}

          {/* Product Attributes */}
          <ProductAttributesDisplay product={product} showSKU={true} />

          {/* Delivery Information */}
          {product.delivery && (
            <DeliveryInfo delivery={product.delivery} className="mt-2" />
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 mt-3">
          <FavoriteButton
            product={product}
            className="z-10 cursor-pointer w-10 h-10 rounded-md bg-colSuperLight flex items-center justify-center transition-all duration-200 hover:scale-110"
          />
          <RemoveFromCartButton
            product={product}
            withConfirmation={true}
            className="z-10 cursor-pointer w-10 h-10 rounded-md bg-colSuperLight flex items-center justify-center transition-all duration-200 hover:scale-110"
          />
        </div>
      </div>

      {/* Price Display */}


      {/* Quantity Control */}
      <div className="basis-1/6 shrink-0">
        <QuantityControl
          product={product}
          enableRemove={true}
          showMessage={true}
        />
      </div>

      {/* Total Price */}
      <div className="basis-1/6 shrink-0">

        <div className="basis-1/6 shrink-0 flex flex-col items-end">
        <PriceDisplay
          price={{
            ...product?.price,
            total: totalPrice
          }}
          variant="total"
          alignment="right"
        />

        {/* Per Unit Price */}
        {product.price && typeof product.price.final === 'number' && (
          <div className="text-xs text-colDarkGray mt-1 text-right">
            {`${product.price.final} ${product.price.currency?.symbol || ''}/${product.price.unit || 'шт'}`}
          </div>
        )}
      </div>
      </div>
    </div>
  );
};
