@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth !important;
}

body {
  font-family: 'Raleway', sans-serif;
  margin: 0;
  overflow: hidden;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  list-style: none;
  text-decoration: none;
}
.content {
  max-width: 1420px;
  width: 100%;
  margin: 0 auto;
  padding-left: 16px;
  padding-right: 16px;
}
@media screen and (max-width: 480px) {
  .content {
    padding-left: 12px;
    padding-right: 12px;
  }
}

/* Slider arrows: */
.slider .swiper-button-prev:after,
.slider .swiper-button-next:after {
  font-size: 20px !important;
  background: #fff !important;
  min-width: 40px;
  height: 40px;
  color: #15765b;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
}
.swiper-pagination-bullet {
  background: #fff;
}
.brands .swiper-pagination-bullet {
  background: #15765b;
}
.brands .swiper-pagination {
  bottom: 20px;
}
.brands .swiper-button-prev,
.brands .swiper-button-next {
  top: 35%;
}
/* Scrollbar: */
::-webkit-scrollbar {
  width: 6px !important;
  border-radius: 24px !important;
}
::-webkit-scrollbar-thumb {
  background: #777 !important;
}
::-webkit-scrollbar-track {
  background: #bdbdbd !important;
}
.scrollable::-webkit-scrollbar {
  height: 2px;
  width: px !important;
  border-radius: 24px !important;
}

.scrollable::-webkit-scrollbar-thumb {
  background-color: #777 !important;
  border-radius: 4px !important;
}

.scrollable::-webkit-scrollbar-track {
  background-color: #bdbdbd !important;
}
.scrollable2::-webkit-scrollbar {
  height: 2px;
  width: 8px !important;
  border-radius: 24px !important;
}

.scrollable2::-webkit-scrollbar-thumb {
  background-color: #d1d1d100 !important;
  transition: background-color 0.2s ease-in-out !important;
  border-radius: 4px !important;
}

.scrollable2:hover::-webkit-scrollbar-thumb {
  background-color: #d1d1d1 !important;
  border-radius: 4px !important;
}

.scrollable2::-webkit-scrollbar-track {
  background-color: transparent !important;
}

.hide-scrollable::-webkit-scrollbar {
  height: 0 !important;
  width: 0 !important;
  border-radius: 0 !important;
}

.css-o4b71y-MuiAccordionSummary-content.Mui-expanded {
  margin: 6px 0 !important;
}

/* Pagination styles */
.pagination-mui li button {
  border: 1px solid #15765b !important;
  color: #15765b !important;
  margin-top: 6px !important;
}
.pagination-mui li .Mui-selected {
  background: #15765b !important;
  color: #fff !important;
}
.pagination-mui li button:hover {
  background: #15765b !important;
  color: #fff !important;
}
/* // Remove input type number arrows: */
input[type='number'] {
  -moz-appearance: textfield;
}

input {
  box-shadow: none !important;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

/* // for Edge */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

/* PRODUCT GALLERY */

.gallery .lg-backdrop {
  background: transparent;
}

.gallery .lg-thumb-outer {
  background: transparent;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
