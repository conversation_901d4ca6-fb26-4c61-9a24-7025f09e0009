import type { Product } from '@/entities/product';

interface StockStatusProps {
  product: Product;
  className?: string;
}

export const StockStatus = ({ product, className = '' }: StockStatusProps) => {
  return (
    <div className={`py-0.5 px-2 bg-colLightGray rounded-lg text-xs font-medium ${className} w-fit`}>
      {product.availability?.stock === 0
        ? 'нет в наличии'
        : `в наличии: ${product.availability?.stock}`}
    </div>
  );
};
