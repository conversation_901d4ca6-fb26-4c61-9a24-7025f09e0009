import { useCallback, useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';

import {
  useSendComparisonMutation,
  useRemoveFromComparisonMutation,
  useGetComparisonQuery,
  setComparison,
  addToComparison,
  removeFromComparison,
} from '@/features/comparison';
import { useAuthContext } from '@/entities/user/model/AuthContext';
import {
  saveToSessionStorage,
  getFromSessionStorage,
} from '@/features/storage/lib';

import type { Product } from '@/entities/product';
import type { RootState } from '@/app/providers/store';
import type { ProductListRequest } from '@/shared/types/ProductListRequest';

/**
 * Enhanced hook for accessing and managing comparison data throughout the application.
 * This is the single source of truth for comparison data.
 *
 * Features:
 * - Handles both authenticated and unauthenticated states
 * - Provides access to comparison data from Redux store
 * - Manages comparison data fetching with optimized caching
 * - Provides helper methods for finding items in comparison
 * - Includes methods for syncing with server and storage
 */
export const useComparison = () => {
  const dispatch = useDispatch();
  const { isAuthenticated } = useAuthContext();
  const comparison = useSelector((state: RootState) => state.comparison.comparison);

  // RTK Query hooks
  const {
    isLoading,
    error,
    refetch: refetchComparison,
  } = useGetComparisonQuery(undefined, {
    skip: !isAuthenticated,
    refetchOnMountOrArgChange: false,
    refetchOnFocus: false,
    refetchOnReconnect: false,
  });

  const [sendComparison] = useSendComparisonMutation();
  const [removeComparison] = useRemoveFromComparisonMutation();

  // State for individual product operations
  const [isUpdating, setIsUpdating] = useState(false);

  // Helper function to check if a product is in comparison
  const isInComparison = useCallback(
    (productId: number) => {
      return comparison.some((item) => item.id === productId);
    },
    [comparison]
  );

  // Helper function to find a product in comparison by ID
  const findComparisonItem = useCallback(
    (productId: number) => {
      return comparison.find((item) => item.id === productId) || null;
    },
    [comparison]
  );

  // Load comparison from sessionStorage
  const loadFromStorage = useCallback(() => {
    try {
      const storedComparison = getFromSessionStorage('comparison') as Product[] | null;
      if (storedComparison && Array.isArray(storedComparison)) {
        dispatch(setComparison(storedComparison));
      }
    } catch (error) {
      console.error('[useComparison] Error loading from storage:', error);
    }
  }, [dispatch]);

  // Save comparison to sessionStorage
  const saveToStorage = useCallback(
    (comparisonData = comparison) => {
      try {
        if (!isAuthenticated && comparisonData) {
          saveToSessionStorage('comparison', comparisonData);
        }
      } catch (error) {
        console.error('[useComparison] Error saving to storage:', error);
      }
    },
    [isAuthenticated, comparison]
  );

  const refreshFromServer = useCallback(async () => {
    if (isAuthenticated) {
      try {
        const result = await refetchComparison();

        if (result.data?.data) {
          dispatch(setComparison(result.data.data));
        }

        return result;
      } catch (error) {
        console.error('[useComparison] Error fetching from server:', error);
        throw error;
      }
    }
    return Promise.resolve({ data: comparison });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, refetchComparison]);

  const syncToServer = useCallback(
    async (comparisonData = comparison) => {
      if (!isAuthenticated || !comparisonData?.length) {
        return Promise.resolve();
      }

      try {
        const payload: ProductListRequest = {
          ids: comparisonData.map((item) => item.id),
        };

        return await sendComparison(payload);
      } catch (error) {
        console.error('[useComparison] Error syncing to server:', error);
        return Promise.reject(error);
      }
    },
    [isAuthenticated, comparison, sendComparison]
  );

  const clearData = useCallback(() => {
    dispatch(setComparison([]));
    sessionStorage.removeItem('comparison');
  }, [dispatch]);

  // Auto-save to storage when comparison changes (for non-authenticated users)
  useEffect(() => {
    if (!isAuthenticated && comparison && comparison.length >= 0) {
      saveToStorage(comparison);
    }
  }, [comparison, isAuthenticated, saveToStorage]);

  // Individual product operations
  const handleComparisonClick = useCallback(
    async (product: Product, e?: React.MouseEvent) => {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }

      setIsUpdating(true);
      try {
        const isCurrentlyInComparison = isInComparison(product.id);

        if (isCurrentlyInComparison) {
          // Optimistically remove from local state
          dispatch(removeFromComparison(product));

          if (isAuthenticated) {
            const result = await removeComparison({ id: product.id });
            if ('error' in result) {
              // Revert on error
              dispatch(addToComparison(product));
              toast.error('Failed to remove from comparison');
            }
          }
        } else {
          // Optimistically add to local state
          dispatch(addToComparison(product));

          if (isAuthenticated) {
            const result = await sendComparison({ id: product.id });
            if ('error' in result) {
              // Revert on error
              dispatch(removeFromComparison(product));
              toast.error('Failed to add to comparison');
            }
          }
        }
      } catch (error) {
        // Revert on error
        const isCurrentlyInComparison = isInComparison(product.id);
        if (isCurrentlyInComparison) {
          dispatch(addToComparison(product));
        } else {
          dispatch(removeFromComparison(product));
        }
        toast.error('Failed to update comparison');
      } finally {
        setIsUpdating(false);
      }
    },
    [isAuthenticated, isInComparison, dispatch, sendComparison, removeComparison]
  );

  return {
    // Data and state
    comparison,
    isLoading: isLoading || isUpdating,
    isError: !!error,

    // Helper functions
    isInComparison,
    findComparisonItem,

    // Individual product operations
    handleComparisonClick,

    // Sync methods (for SyncProvider)
    loadFromStorage,
    saveToStorage,
    refreshFromServer,
    syncToServer,
    clearData,
  };
};