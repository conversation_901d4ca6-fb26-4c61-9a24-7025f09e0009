// src/entities/user/lib/hooks/useAuth.ts
import { useCallback } from 'react';
import { useAuthContext } from '../../model/AuthContext';
import { useSyncUserData } from '@/features/auth/hooks/useSyncUserData';

/**
 * Enhanced hook for authentication that handles data synchronization
 * Now uses AuthContext to avoid circular dependencies
 */
export const useAuth = () => {
  const auth = useAuthContext();
  const { clearOnLogout, syncOnLogin } = useSyncUserData();

  // Enhanced logout that also clears user data
  const enhancedLogout = useCallback(() => {
    auth.logout();
    clearOnLogout();
  }, [auth, clearOnLogout]);

  // Enhanced authenticate that also syncs user data
  const enhancedAuthenticateWithPhone = useCallback(
    async (sessionId: string, additionalData?: Record<string, unknown>) => {
      const result = await auth.authenticateWithPhone(
        sessionId,
        additionalData
      );

      if (result.success) {
        // Sync user data in the background
        void syncOnLogin();
      }

      return result;
    },
    [auth, syncOnLogin]
  );

  return {
    ...auth,
    logout: enhancedLogout,
    authenticateWithPhone: enhancedAuthenticateWithPhone,
  };
};
