// src/entities/filter/api/filterApi.ts
import { api } from '@/shared/api/api';

import type { GetFiltersRequest, GetFiltersResponse } from './types';

export const filterApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getFilters: builder.mutation<GetFiltersResponse, GetFiltersRequest>({
      query: (params) => ({
        url: '/api/Products/filters',
        method: 'POST',
        body: params
      }),
      // Add query caching for 30 seconds to prevent duplicate requests
      keepUnusedDataFor: 30,
      // Deduplicate identical requests during that window
      extraOptions: {
        maxRetries: 0 // Disable retries for filters
      },
      // Force sequential execution
      async onQueryStarted(args, { dispatch, queryFulfilled, getCacheEntry }) {
        try {
          // Wait for any existing mutations to complete
          const existingEntry = getCacheEntry();
          if (existingEntry?.status === 'pending') {
            await queryFulfilled;
          }
        } catch {}
      },
      // Serialize request for cache key
      serializeQueryArgs: ({ queryArgs }) => {
        return JSON.stringify(queryArgs);
      },
      // Only trigger if args changed
      forceRefetch: ({ currentArg, previousArg }) => {
        return JSON.stringify(currentArg) !== JSON.stringify(previousArg);
      }
    }),
    getFiltersNew: builder.mutation<any, { category_id: string; filters: any }>({
      query: (params) => ({
        url: '/api/ProductsTest/filters',
        method: 'POST',
        body: params
      }),
      // Deduplicate identical requests during that window
      extraOptions: {
        maxRetries: 0 // Disable retries for filters
      },
      // Force sequential execution
      async onQueryStarted(args, { dispatch, queryFulfilled, getCacheEntry }) {
        try {
          // Wait for any existing mutations to complete
          const existingEntry = getCacheEntry();
          if (existingEntry?.status === 'pending') {
            await queryFulfilled;
          }
        } catch {}
      },
      // Serialize request for cache key
      serializeQueryArgs: ({ queryArgs }) => {
        return JSON.stringify(queryArgs);
      },
      // Only trigger if args changed
      forceRefetch: ({ currentArg, previousArg }) => {
        return JSON.stringify(currentArg) !== JSON.stringify(previousArg);
      }
    }),
    getBasicFilters: builder.query<any, void>({
      query: () => '/api/Products/filters/basic',
      keepUnusedDataFor: 60
    })
  })
});

export const { useGetFiltersMutation, useGetBasicFiltersQuery, useGetFiltersNewMutation } =
  filterApi;
