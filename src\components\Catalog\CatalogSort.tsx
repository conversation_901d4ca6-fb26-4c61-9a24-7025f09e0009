import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';

interface CatalogSortProps {
  currentSort: any;
  sortBy: any;
  handleSortChange: any;
}

export const CatalogSort: React.FC<CatalogSortProps> = ({
  currentSort,
  handleSortChange,
  sortBy
}) => {
  return (
    <Select
      value={`${currentSort.orderBy}-${currentSort.sortOrder}`}
      onValueChange={handleSortChange}
    >
      <SelectTrigger className='w-[280px] py-[13px] px-[16px] text-[16px] h-[48px] flex items-center shadow-md focus:shadow-md focus:ring-0 focus:ring-offset-0 rounded-[12px] border-[#FBFBFB]'>
        <SelectValue placeholder='Сортировать по' />
      </SelectTrigger>
      <SelectContent className='w-[280px] rounded-[12px] p-[2px] border-[#FBFBFB] shado-md '>
        {sortBy.map((sort) => (
          <SelectItem
            key={`${sort.orderBy}-${sort.sortOrder}`}
            className='rounded-[10px] data-[state=checked]:text-colGreen border-[#FBFBFB]'
            value={`${sort.orderBy}-${sort.sortOrder}`}
          >
            {sort.title}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
