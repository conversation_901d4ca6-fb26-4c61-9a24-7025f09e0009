// src/widgets/product-card/model/hooks/useProductCard.ts
import { useEffect, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';

import type { RootState } from '@/app/providers/store';
import type { Product } from '@/entities/product';
import type { CartProduct } from '@/features/cart';
import type { PriceType } from '@/entities/price';

export const useProductCard = (product: Product) => {
  // Use selector directly to get cart data from Redux store
  const cartItems = useSelector((state: RootState) => state.cart.cart);

  // Use state to track the product price
  const [productPrice, setProductPrice] = useState<PriceType>(product.price);

  // Find the product in the cart
  const productInCart = cartItems.find(item => item.id === product.id) as CartProduct | undefined;

  // Helper function to check if the product is in the cart
  const isInCart = useCallback(() => {
    return !!productInCart;
  }, [productInCart]);

  // Update the product price whenever the cart items change
  useEffect(() => {
    if (productInCart) {
      setProductPrice(productInCart.price);
    } else {
      setProductPrice(product.price);
    }
  }, [productInCart, product.price, cartItems]);

  return {
    productInCart,
    productPrice,
    isInCart,
  };
};
