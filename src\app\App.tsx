import React from 'react';

import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { RouterProvider } from 'react-router-dom';

import { store } from '@/app/providers/store';
import { router } from '@/app/routing/router';
import { AuthProvider } from '@/entities/user/model/AuthContext';
import { SyncProvider } from '@/app/providers/SyncProvider';

import '@/app/index.css';
import ErrorBoundary from '@/shared/ui/ErrorBoundary/ErrorBoundary';

export default function initializeApp(): void {
  const rootElement = document.getElementById('root');
  if (!rootElement) throw new Error('Failed to find the root element');

  const root = ReactDOM.createRoot(rootElement);

  root.render(
    <ErrorBoundary>
      <Provider store={store}>
        <AuthProvider>
          <SyncProvider>
            <RouterProvider router={router} />
          </SyncProvider>
        </AuthProvider>
      </Provider>
    </ErrorBoundary>
  );
}
