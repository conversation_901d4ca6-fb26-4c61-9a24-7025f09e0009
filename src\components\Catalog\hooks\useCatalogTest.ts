import { useGetFiltersNewMutation } from '@/entities/filter';

import { useGetCategoryTreeQuery } from '@/entities/category';
import { useGetVariantsTestMutation } from '@/entities/product';
import {
  buildQueryParams,
  buildServerFilter
} from '@/shared/lib/catalog-filter/catalog-filter.utils';
import { getFiltersFromUrl } from '@/shared/lib/getFiltersFromUrl';
import { modalFilterItemObserver } from '@/shared/lib/observer';
import { bodyScrollLockSingleObserver } from '@/shared/lib/observer/body.observer';
import { updateFilterState } from '@/shared/lib/queryUtils';
import { PrevTreeNode, TreeNode } from '@/types/CategorieTree';
import { ProductFilter as IProductFilter } from '@/types/Filters/ProductFilter';
import { ProductFilterURL } from '@/types/Filters/ProductFilterURL';
import { OrderByType, SortOrderType } from '@/types/Filters/Sort';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ProductFilterURLInput } from '../CatalogRoot';
import { TransformCategoryToTree } from '../utils/transformCategory';
import { useSort } from './useSort';

export const useCatalogTest = () => {
  const { handleSortChange: onSortChange, currentSort, sortBy } = useSort();
  const navigate = useNavigate();
  const [filters, setFilters] = useState<IProductFilter[]>([]);
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [chips, setChips] = useState<ProductFilterURL[]>([]);
  const [productsPage, setProductsPage] = useState<number>(1);
  const [products, setProducts] = useState<any[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [categoryTree, setCategoryTree] = useState<TreeNode>();
  const [prevCategories, setPrevCategories] = useState<PrevTreeNode[]>([]);

  const observer = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  const { categoryId } = useParams();

  const [fetchFiltersProducts] = useGetVariantsTestMutation();
  const [fetchFilters] = useGetFiltersNewMutation();

  const cardView = localStorage.getItem('cardView');
  const [cardType, setTypeCard] = useState(cardView ? cardView : 'tile');

  const { data: categories } = useGetCategoryTreeQuery(categoryId);

  const handleSortChange = async (value: any) => {
    onSortChange(value);
    setProductsPage(1);
    const [orderBy, sortOrder] = value.split('-') as [OrderByType, SortOrderType];

    const selectedSort = sortBy.find(
      (sort) => sort.orderBy === orderBy && sort.sortOrder === sortOrder
    );

    if (selectedSort) {
      setIsLoadingProducts(true);

      const { filters } = getFiltersFromUrl();

      try {
        const resultProducts = await fetchFiltersProducts({
          filters,
          category_id: categoryId,
          limit: 20,
          page: 1,
          orderBy: selectedSort.orderBy,
          sortOrder: selectedSort.sortOrder
        });
        if ('data' in resultProducts) {
          setProducts(resultProducts.data.data);
          setIsLoadingProducts(false);
        }
      } catch (error) {
        console.log(error);
      }
    }
  };

  const onChangeFilter = async (input: ProductFilterURLInput) => {
    setFiltersLoading(true);
    setIsLoadingProducts(true);
    setProductsPage(1);

    const filtersArray = 'alone' in input ? [input.alone] : input.multiple;

    let { filterParams: queryParamsToFilterData } = getFiltersFromUrl();

    filtersArray.forEach((filter) => {
      queryParamsToFilterData = updateFilterState(queryParamsToFilterData, filter);
    });

    const dataToFilterData = queryParamsToFilterData;

    const queryParams = buildQueryParams(dataToFilterData);
    const filterDataToServerData = buildServerFilter(dataToFilterData);

    const res = async () => {
      const r = await fetchFilters({
        category_id: categoryId,
        filters: filterDataToServerData
      });
      if ('data' in r) {
        setFilters(r.data.data);
        return r.data.data;
      }
    };
    const p = await fetchFiltersProducts({
      filters: filterDataToServerData,
      category_id: categoryId,
      limit: 20,
      page: 1,
      orderBy: currentSort.orderBy,
      sortOrder: currentSort.sortOrder
    });
    if ('data' in p) {
      setProducts(p.data.data);
      setIsLoadingProducts(false);
      setProductsPage((prev) => prev + 1);
    }
    const data = await res();
    navigate(`?${queryParams.join('&')}`, { replace: true });

    const { filterParams: filterData } = getFiltersFromUrl();

    const items = createProductFilterURL(data, filterData);
    setChips(items);

    setFiltersLoading(false);
  };

  const onResetFilters = async () => {
    setFiltersLoading(true);
    const queryParams = [];
    navigate(`?${queryParams.join('&')}`, { replace: true });

    const r = await fetchFilters({
      category_id: categoryId,
      filters: {}
    });

    if ('data' in r) {
      setFilters(r.data.data);
    }
    const p = await fetchFiltersProducts({
      filters: {},
      categoryId,
      limit: 20,
      page: 1,
      orderBy: currentSort.orderBy,
      sortOrder: currentSort.sortOrder
    });
    if ('data' in p) {
      setProducts(p.data.data);
      setIsLoadingProducts(false);
    }
    setChips([]);
    setFiltersLoading(false);
  };

  const handleOpenModalAllFilters = () => {
    bodyScrollLockSingleObserver.setValue(true);
    const modalId = modalFilterItemObserver.addObserver({
      type: 'all',
      data: filters,
      onAccept: async (arr, params) => {
        setFilters(arr);
        navigate(`?${params.join('&')}`, { replace: true });
        bodyScrollLockSingleObserver.setValue(false);
        const { filterParams: filterData } = getFiltersFromUrl();
        const filterDataToServerData = buildServerFilter(filterData);
        const p = await fetchFiltersProducts({
          filters: filterDataToServerData,
          category_id: categoryId,
          limit: 20,
          page: 1,
          orderBy: currentSort.orderBy,
          sortOrder: currentSort.sortOrder
        });
        if ('data' in p) {
          setProducts(p.data.data);
          setIsLoadingProducts(false);
        }

        const items = createProductFilterURL(arr, filterData);
        setChips(items);
        modalFilterItemObserver.dissmissObserver(modalId);
      },
      onCancel: () => {
        modalFilterItemObserver.dissmissObserver(modalId);
        setTimeout(() => {
          bodyScrollLockSingleObserver.setValue(false);
        }, 300);
      }
    });
  };

  const createProductFilterURL = (
    filterData: IProductFilter[],
    userValues: unknown
  ): ProductFilterURL[] => {
    const result: ProductFilterURL[] = [];

    for (const [key, filter] of Object.entries(userValues)) {
      if (filter.type === 'multiple') {
        const parentId = key;
        const foundFilter = filterData.find((f) => f.id == parentId);
        if (foundFilter && foundFilter.input_type === 'multiple') {
          for (const val of filter.value) {
            const matched = foundFilter.values.find((v) => v.id == val);
            if (matched) {
              result.push({
                type: 'multiple',
                parentId,
                id: matched.id,
                title: foundFilter.name,
                text: matched.text,
                value: matched.id
              });
            }
          }
        }
      }

      if (filter.type === 'range') {
        const range = filter.value;
        result.push({
          type: 'range',
          parentId: key,
          title: 'Цена',
          value: [+range.from, +range.to]
        });
      }
    }

    return result;
  };

  const firstfetchFilters = () => {
    const { filters } = getFiltersFromUrl();

    const res = async () => {
      setFiltersLoading(true);
      setIsLoadingProducts(true);
      const r = await fetchFilters({
        category_id: categoryId,
        filters
      });
      const p = await fetchFiltersProducts({
        filters,
        category_id: categoryId,
        limit: 20,
        page: 1,
        orderBy: currentSort.orderBy,
        sortOrder: currentSort.sortOrder
      });
      if ('data' in p) {
        setProducts(p.data.data);
        setProductsPage(2);
      }
      if ('data' in r) {
        setFilters(r.data.data);
        setFiltersLoading(false);
        setIsLoadingProducts(false);

        const { filterParams: filterData } = getFiltersFromUrl();

        const items = createProductFilterURL(r.data.data, filterData);
        setChips(items);
      }
    };
    res();
  };

  const loadProducts = useCallback(async () => {
    if (isLoadingProducts || !hasMore) return;

    setIsLoadingProducts(true);

    try {
      const { filters } = getFiltersFromUrl();

      const p = await fetchFiltersProducts({
        filters,
        category_id: categoryId,
        limit: 20,
        page: productsPage,
        orderBy: currentSort.orderBy,
        sortOrder: currentSort.sortOrder
      });
      if ('data' in p) {
        setProducts((prev) => [...prev, ...p.data.data]);
        setIsLoadingProducts(false);
        setProductsPage(productsPage + 1);
        setHasMore(p.data.data.length > 0);
      }
    } catch (error) {
      console.error(error);
      setIsLoadingProducts(false);
    }
  }, [isLoadingProducts, productsPage, hasMore]);

  useEffect(() => {
    if (categories) {
      const prevCategories = categories.category_chain.map(({ id, name, slug }) => ({
        id,
        label: name,
        link: slug
      }));
      setPrevCategories(prevCategories.splice(0, prevCategories.length - 1));
      setCategoryTree({
        id: 'catalog',
        children: TransformCategoryToTree(categories.children),
        label: 'Каталог',
        link: ''
      });
    }

    return () => {};
  }, [categories]);

  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingProducts) {
          loadProducts();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.current.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current && observer.current) {
        observer.current.unobserve(loadMoreRef.current);
      }
    };
  }, [loadProducts, hasMore, isLoadingProducts]);

  useEffect(() => {
    setProductsPage(1);
    setFilters([]);
    setHasMore(true);
    firstfetchFilters();

    window.scrollTo(0, 0);
  }, [categoryId]);

  useEffect(() => {
    if ('scrollRestoration' in window.history) {
      window.history.scrollRestoration = 'manual';
    }
    window.scrollTo(0, 0);
  }, []);

  return {
    sortBy,
    handleSortChange,
    currentSort,
    cardType,
    setTypeCard,
    filtersLoading,
    onChangeFilter,
    onResetFilters,
    filters,
    handleOpenModalAllFilters,
    chips,
    products,
    isLoadingProducts,
    hasMore,
    loadMoreRef,
    categories,
    categoryTree,
    prevCategories
  };
};
