import { api } from '@/shared/api/api';
import { updateCartTotals } from '../model/cartSlice';

import type {
  GetUserCartResponse,
  SendCartResponse,
  SendCartRequest,
} from './types';
import type { AdditionalServerResponseData } from '@/shared/types/AdditionalServerResponseData';
import type { ProductListRequest } from '@/shared/types/ProductListRequest';

export const cartEndpoints = api.injectEndpoints({
  endpoints: (builder) => ({
    getSuggestions: builder.mutation({
      query: (params) => ({
        url: '/api/Products/search/suggestions',
        method: 'POST',
        body: params,
      }),
    }),
    getUserCart: builder.query<GetUserCartResponse, void>({
      query: () => '/api/ProductsCart/get',
      providesTags: [{ type: 'Cart', id: 'LIST' }],
    }),
    sendCart: builder.mutation<SendCartResponse, SendCartRequest>({
      query: (data) => ({
        url: '/api/ProductsCart/set',
        method: 'POST',
        body: data,
      }),
      // Don't invalidate any tags to prevent automatic refetching
      // We'll rely on explicit refetching when needed
      invalidatesTags: () => [],
      // Transform the response to update the Redux store directly
      // This allows us to avoid making a separate getUserCart request
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;

          // If the request was successful and we have cart data
          if (data.success === 'ok') {
            // Update the cart totals in Redux directly
            // This eliminates the need for a separate UserData/get request
            dispatch(
              updateCartTotals({
                total_amount: data.total_amount,
                total_quantity: data.total_quantity,
              })
            );
          }
        } catch {
          // Error handling is done by the API layer
        }
      },
    }),
    removeFromCart: builder.mutation<
      AdditionalServerResponseData,
      ProductListRequest
    >({
      query: (data) => ({
        url: '/api/ProductsCart/delete',
        method: 'POST',
        body: data,
      }),
      // Don't invalidate any tags to prevent automatic refetching
      // We'll rely on explicit refetching when needed
      invalidatesTags: () => [],
      // Instead, manually update the cache
      async onQueryStarted(_arg, { queryFulfilled }) {
        try {
          await queryFulfilled;
          // No need to update the cache here - the Redux store is already updated
          // by the cart slice, and components read from there
        } catch {
          // Error handling is done by the API layer
        }
      },
    }),
    getCartShareCode: builder.mutation({
      query: () => ({
        url: '/api/ProductsCart/share/create',
        method: 'POST',
      }),
    }),
    getCartShareItemsByCode: builder.mutation({
      query: (data) => ({
        url: '/api/ProductsCart/share/get',
        method: 'POST',
        body: data,
      }),
    }),
    addSharedCart: builder.mutation({
      query: (data) => ({
        url: '/api/ProductsCart/share/add',
        method: 'POST',
        body: data,
      }),
      // Don't invalidate any tags to prevent automatic refetching
      // We'll rely on explicit refetching when needed
      invalidatesTags: () => [],
      // Instead, manually update the cache
      async onQueryStarted(_arg, { queryFulfilled }) {
        try {
          await queryFulfilled;
          // No need to update the cache here - the Redux store is already updated
          // by the cart slice, and components read from there
        } catch {
          // Error handling is done by the API layer
        }
      },
    }),
  }),
});

// Export hooks for cart endpoints
export const {
  useSendCartMutation,
  useGetSuggestionsMutation,
  useGetUserCartQuery,
  useRemoveFromCartMutation,
  useGetCartShareCodeMutation,
  useGetCartShareItemsByCodeMutation,
  useAddSharedCartMutation,
} = cartEndpoints;
