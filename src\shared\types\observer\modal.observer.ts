import { TreeNode } from '@/types/CategorieTree';
import { ProductFilter, ProductFilterMultiple } from '@/types/Filters/ProductFilter';

type ModalTypes = 'item' | 'all' | 'category';

interface BaseModal {
  type: ModalTypes;
}

export interface FilterItemModal extends BaseModal {
  type: 'item';
  data: ProductFilterMultiple;
  onAccept: (data: any) => void;
  onClose: () => void;
}

export interface FilterAllModal extends BaseModal {
  type: 'all';
  data: ProductFilter[];
  onCancel: () => void;
  onAccept: (data: ProductFilter[], params: string[]) => void;
}

export interface FilterCategoryModal extends BaseModal {
  type: 'category';
  data: TreeNode;
  onCancel: () => void;
}

type ModalMap = {
  all: FilterAllModal;
  item: FilterItemModal;
  category: FilterCategoryModal;
};

export type ModalFilterData = ModalMap[keyof ModalMap];
export type ModalFilterItem = ModalMap['item'];
export type ModalFilterAll = ModalMap['all'];
export type ModalFilterCategory = ModalMap['category'];
