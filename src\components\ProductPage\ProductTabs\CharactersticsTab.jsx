import React from 'react'

function CharactersticsTab({ current, group, setTabIndex }) {

    return (
        <>
            <h3 className='text-2xl my-5 font-semibold'>Характеристики</h3>

            <div className='flex flex-wrap flex-row gap-[20px]'>
                <div className='flex items-end basis-[calc(100%/2-10px)]'>
                    <div className='shrink leading-none text-colDarkGray mr-1'>Код товара</div>
                    <div className='grow border-b-2 border-dotted'></div>
                    <div className='flex items-end leading-none shrink ml-1'>
                        {current?.sku}
                    </div>
                </div>
                {
                    current?.attributes?.map((attribute, index) => {

                        {/* Если атрибут модификационный выводит значение актуальное для модификации, если нет, то общее значение атрибута */}

                        if (current?.attributes?.some( modAttr => Number(modAttr.id) === Number(attribute.id))) {
                            return(
                                <div className='flex items-end basis-[calc(100%/2-10px)]'>
                                    <div className='shrink self-start leading-none text-colDarkGray mr-1'>{attribute.name}</div>
                                    <div className='grow self-start h-4 border-b-2 border-dotted'></div>
                                    <div className='flex text-end leading-none shrink ml-1 max-w-[50%] break-all'>
                                    {current?.attributes?.find( modAttr => Number(modAttr.id) === Number(attribute.id)).text}
                                    </div>
                                </div>
                            ) 
                        } else if ( attribute.values[0].text ) {
                            return(
                                <div className='flex items-end basis-[calc(100%/2-10px)]'>
                                <div className='shrink self-start leading-none text-colDarkGray mr-1'>{attribute.name}</div>
                                <div className='grow self-start h-4 border-b-2 border-dotted'></div>
                                <div className='flex text-end leading-none shrink ml-1 max-w-[50%] break-all'>
                                    { attribute.values[0].text }
                                </div>
                            </div>
                            ) 
                        }
                    })

                }
                {
                    group?.attributes?.map((attribute, index) => {

                        {/* Если атрибут модификационный выводит значение актуальное для модификации, если нет, то общее значение атрибута */}

                        if (current?.attributes?.some( modAttr => Number(modAttr.id) === Number(attribute.id))) {
                            return(
                                <div className='flex items-end basis-[calc(100%/2-10px)]'>
                                    <div className='shrink self-start leading-none text-colDarkGray mr-1'>{attribute.name}</div>
                                    <div className='grow self-start h-4 border-b-2 border-dotted'></div>
                                    <div className='flex text-end leading-none shrink ml-1 max-w-[50%] break-all'>
                                    {current?.attributes?.find( modAttr => Number(modAttr.id) === Number(attribute.id)).text}
                                    </div>
                                </div>
                            ) 
                        } else if ( attribute.values[0].text ) {
                            return(
                                <div className='flex items-end basis-[calc(100%/2-10px)]'>
                                <div className='shrink self-start leading-none text-colDarkGray mr-1'>{attribute.name}</div>
                                <div className='grow self-start h-4 border-b-2 border-dotted'></div>
                                <div className='flex text-end leading-none shrink ml-1 max-w-[50%] break-all'>
                                    { attribute.values[0].text }
                                </div>
                            </div>
                            ) 
                        }
                    })

                }
            </div>


            {group?.description && <><h3 className='text-2xl mt-5 mb-[10px] font-semibold'>Описание</h3>
            <div className='text-[14px]'>
                
                { group?.description }
            </div></>}

            
        </>
    )
}

export default CharactersticsTab