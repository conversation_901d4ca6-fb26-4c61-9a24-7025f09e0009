export const buildQueryParams = (filters: Record<string, any>) => {
  const queryParams: string[] = [];

  Object.entries(filters).forEach(([parentId, filter]) => {
    if (filter.type === 'multiple') {
      const joinedValues = filter.value.join('|');
      queryParams.push(`filter_${parentId}=${joinedValues}`);
    }
    if (filter.type === 'range') {
      const { from, to } = filter.value;
      queryParams.push(`range_${parentId}=${from}-${to}`);
    }
  });

  return queryParams;
};

export const buildServerFilter = (filters: Record<string, any>) => {
  return Object.entries(filters).reduce(
    (acc, [key, value]) => {
      if (value.type === 'multiple' || value.type === 'range') {
        acc[key] = value.value;
      }
      return acc;
    },
    {} as Record<string, any>
  );
};
