// src/entities/user/model/userSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

import {
  getTokenFromCookies,
  setTokenInCookies,
  removeTokenFromCookies,
} from '../lib/cookies';

import type { User, UserState } from './types';
import type { PayloadAction } from '@reduxjs/toolkit';

const initialState: UserState = {
  token: null,
  isAuthenticated: false,
  isInitialized: false,
  data: null,
  tokenSource: null,
};

/**
 * Initialize authentication asynchronously
 */
export const initializeAuthAsync = createAsyncThunk(
  'user/initializeAuthAsync',
  async () => {
    try {
      // Simple approach: just get token from cookies
      const token = getTokenFromCookies();
      return { token, source: 'furnica' };
    } catch (error) {
      console.error('[Auth] Error initializing auth:', error);
      return { token: null, source: null };
    }
  }
);

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // Synchronous initialization (fallback)
    initializeAuth: (state) => {
      const token = getTokenFromCookies();

      // If we have a token, use it
      if (token) {
        state.token = token;
        state.isAuthenticated = true;
        state.tokenSource = 'furnica';
      } else {
        state.token = null;
        state.isAuthenticated = false;
        state.tokenSource = null;
      }

      state.isInitialized = true;
    },

    // Update token in Redux state
    setToken: (state, action: PayloadAction<{ token: string | null, source?: 'furnica' | null }>) => {
      const { token, source = 'furnica' } = action.payload;

      if (token) {
        // Save token to cookies
        setTokenInCookies(token);
        state.token = token;
        state.isAuthenticated = true;
        state.tokenSource = source;
      } else {
        // Remove token from cookies
        removeTokenFromCookies();
        state.token = null;
        state.isAuthenticated = false;
        state.tokenSource = null;
        state.data = null;
      }

      // Always mark as initialized after any auth action
      state.isInitialized = true;
    },

    logout: (state) => {
      // Remove token from cookies
      removeTokenFromCookies();
      state.token = null;
      state.isAuthenticated = false;
      state.tokenSource = null;
      state.data = null;
      // Keep isInitialized true to prevent loading flashes
    },

    setUserData: (state, action: PayloadAction<User>) => {
      state.data = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(initializeAuthAsync.fulfilled, (state, action) => {
        const { token, source } = action.payload;
        state.token = token;
        state.isAuthenticated = !!token;
        state.tokenSource = source;
        state.isInitialized = true;
      })
      .addCase(initializeAuthAsync.rejected, (state) => {
        // If initialization fails, still mark as initialized
        // but clear authentication state to be safe
        state.token = null;
        state.isAuthenticated = false;
        state.tokenSource = null;
        state.data = null;
        state.isInitialized = true;
      });
  },
});

export const {
  initializeAuth,
  setToken,
  logout,
  setUserData
} = userSlice.actions;
export default userSlice.reducer;
