import { useComparison } from '@/features/comparison';
import { Icon } from '@iconify-icon/react';

import type { Product } from '@/entities/product';

interface ComparisonButtonProps {
  product: Product;
  className?: string;
}

export const ComparisonButton = ({
  product,
  className = '',
}: ComparisonButtonProps) => {
  const { isInComparison: checkIsInComparison, isLoading, handleComparisonClick } = useComparison();

  const isInComparison = checkIsInComparison(product.id);

  return (
    <button
      onClick={(e) => handleComparisonClick(product, e)}
      disabled={isLoading}
      className={`transition-all duration-300 hover:scale-110 ${isLoading ? 'cursor-wait' : 'cursor-pointer'} ${className}`}
    >
      {isInComparison ?
        <Icon icon="solar:chart-2-bold" width={20} height={20} className="text-colGreen"/>
        :
        <Icon icon="solar:chart-2-linear" width={20} height={20} className="text-colGreen"/>
      }
    </button>
  );
};
