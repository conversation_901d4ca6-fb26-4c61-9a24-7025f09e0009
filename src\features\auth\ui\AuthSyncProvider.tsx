// src/features/auth/ui/AuthSyncProvider.tsx
import React, { useEffect } from 'react';
import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useSyncUserData } from '@/features/auth/hooks/useSyncUserData';

/**
 * Component that handles data synchronization after authentication
 * This is separate from AuthProvider to avoid circular dependencies
 */
export const AuthSyncProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const { isAuthenticated, isInitialized } = useAuthContext();
  const { syncOnLogin, clearOnLogout } = useSyncUserData();

  // Handle sync on login
  useEffect(() => {
    if (isAuthenticated && isInitialized) {
      // Sync data in the background when user becomes authenticated
      void syncOnLogin();
    }
  }, [isAuthenticated, isInitialized, syncOnLogin]);

  // Handle clear on logout
  useEffect(() => {
    if (!isAuthenticated && isInitialized) {
      // Clear data when user logs out
      clearOnLogout();
    }
  }, [isAuthenticated, isInitialized, clearOnLogout]);

  return <>{children}</>;
};
