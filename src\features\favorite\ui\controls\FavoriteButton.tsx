import { Icon } from '@iconify-icon/react';

import { useFavorites } from '../../model/hooks/useFavorites';

import type { Product } from '@/entities/product';

interface FavoriteButtonProps {
  product: Product;
  className?: string;
}

export const FavoriteButton = ({
  product,
  className = '',
}: FavoriteButtonProps) => {
  const { isInFavorites, isLoading, handleFavoriteClick } = useFavorites();

  const isInFavorite = isInFavorites(product.id);

  return (
    <button
      onClick={(e) => handleFavoriteClick(product, e)}
      disabled={isLoading}
      className={` ${isLoading ? 'cursor-wait' : 'cursor-pointer'} ${className}`}
    >
      {isInFavorite ? (
        <Icon
          icon="solar:heart-bold"
          width={24}
          height={24}
          className="text-colGreen"
        />
      ) : (
        <Icon
          icon="solar:heart-outline"
          width={24}
          height={24}
          className="text-colGreen"
        />
      )}
    </button>
  );
};
